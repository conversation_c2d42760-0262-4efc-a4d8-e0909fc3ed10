<?php
use Illuminate\Support\Facades\Route;
use App\Models\ReciptMaintaince;
use App\Models\PurchasesOrder;
use App\Models\SalesOrder;
use App\Models\Sales;
use App\Models\Quote;
use App\Models\Purchases;
use App\Models\Transltor;
use App\Models\CompanyData;
use App\Models\Products;
use App\Models\ProductsQty;
use App\Models\ProductUnits;

      Route::get('ScanQR', 'App\Http\Controllers\ResturantController@ScanQR');
      Route::get('getWeight', 'App\Http\Controllers\AdminController@getWeight');
      Route::get('QRUpdate', 'App\Http\Controllers\AdminController@QRUpdate');

    Route::get('test', function () {

   return back();
});






Route::group(['middleware' => 'firewall.all'], function () {      });


$Def=CompanyData::orderBy('id','desc')->first();
    if($Def->View == 0){

    Route::get('/', function () {
   return view('welcome');
});

    }elseif($Def->View == 1){


         Route::get('/', 'App\Http\Controllers\WebsiteController@HomePage');
         Route::get('ProDetails/{id}', 'App\Http\Controllers\WebsiteController@ProductDetails');
         Route::get('PrescriptionPro/{id}', 'App\Http\Controllers\WebsiteController@PrescriptionPro');
         Route::get('BlogsDet/{id}', 'App\Http\Controllers\WebsiteController@BlogsDet');
         Route::get('BlogSite', 'App\Http\Controllers\WebsiteController@BlogSite');
         Route::get('PolicySite', 'App\Http\Controllers\WebsiteController@PolicySite');

    }elseif($Def->View == 2){


         Route::get('/', 'App\Http\Controllers\ResturantController@ResturantView');
    }




Route::group(['middleware' => 'ViewAuthECom'], function () {
            //View  E-Commerce  =============================================

        //ChangeCountrySession
Route::get('HASHING', 'App\Http\Controllers\ElectronicBillController@HASHING');
Route::get('ChangeCountrySession/{id}', 'App\Http\Controllers\WebsiteController@ChangeCountrySession');
Route::get('ChangeCountrySession', 'App\Http\Controllers\WebsiteController@ChangeCountrySession');

    //About
Route::get('AboutSite', 'App\Http\Controllers\WebsiteController@AboutSite');

    //ContactSite
Route::get('ContactSite', 'App\Http\Controllers\WebsiteController@ContactSite');

    //PrivacyPolicySite
Route::get('PrivacyPolicySite', 'App\Http\Controllers\WebsiteController@PrivacyPolicySite');

    //TermsSite
Route::get('TermsSite', 'App\Http\Controllers\WebsiteController@TermsSite');

    //FAQSite
Route::get('FAQSite', 'App\Http\Controllers\WebsiteController@FAQSite');

//BlogsSite
    Route::get('BlogsSite', 'App\Http\Controllers\WebsiteController@BlogsSite');
    Route::get('BlogDetails/{id}', 'App\Http\Controllers\WebsiteController@BlogDetails');


//Shop Site
   Route::get('ShopSite', 'App\Http\Controllers\WebsiteController@ShopSite');
   Route::get('ProductDetails/{id}', 'App\Http\Controllers\WebsiteController@ProductDetails');
   Route::get('FilterShopCat/{id}', 'App\Http\Controllers\WebsiteController@FilterShopCat');
   Route::get('ShopFilterBrand/{id}', 'App\Http\Controllers\WebsiteController@ShopFilterBrand');
   Route::get('ShopFilterName', 'App\Http\Controllers\WebsiteController@ShopFilterName');


            //PostMsgRqst
Route::post('PostMsgRqst', 'App\Http\Controllers\WebsiteController@PostMsgRqst');


//Auth Users
Route::group(['middleware' => 'IFNotAuth:client'], function() {
    Route::get('LoginSite', 'App\Http\Controllers\WebsiteController@LoginSite');
    Route::post('PostLoginSite', 'App\Http\Controllers\WebsiteController@PostLoginSite');
    Route::get('ForgotSite', 'App\Http\Controllers\WebsiteController@ForgotSite');
    Route::post('PostForgotSite', 'App\Http\Controllers\WebsiteController@PostForgotSite');
    Route::get('RegisterSite', 'App\Http\Controllers\WebsiteController@RegisterSite');
    Route::post('PostRegister', 'App\Http\Controllers\WebsiteController@PostRegister');
    Route::post('PostCodeSite', 'App\Http\Controllers\WebsiteController@PostCodeSite');
    Route::post('PostResetPassword', 'App\Http\Controllers\WebsiteController@PostResetPassword');
});
    Route::get('LogoutSite', 'App\Http\Controllers\WebsiteController@LogoutSite');

//Qty Filter
   Route::get('ProductDetails/{id}/SiteProQty', 'App\Http\Controllers\WebsiteController@SiteProQty');
   Route::get('ProductDetails/{id}/SiteProQtyV', 'App\Http\Controllers\WebsiteController@SiteProQtyV');
   Route::get('ProductDetails/{id}/SiteProQtyVV', 'App\Http\Controllers\WebsiteController@SiteProQtyVV');


//Cart
    Route::get('CartSite', 'App\Http\Controllers\WebsiteController@CartSite');
    Route::post('AddToCart', 'App\Http\Controllers\WebsiteController@AddToCart');
    Route::post('UpdateCart', 'App\Http\Controllers\WebsiteController@UpdateCart');
    Route::get('DeleteCart/{id}', 'App\Http\Controllers\WebsiteController@DeleteCart');

//Cupon Code
      Route::post('UpdateCuponCode', 'App\Http\Controllers\WebsiteController@UpdateCuponCode');

//Checkout

    Route::group(['middleware' => 'CartCount:client'], function() {
    Route::get('Checkout', 'App\Http\Controllers\WebsiteController@Checkout');
    Route::get('ChangeAddressSite', 'App\Http\Controllers\WebsiteController@ChangeAddressSite');
    });
//Place Order
   Route::post('PlaceOrder', 'App\Http\Controllers\WebsiteController@PlaceOrder');



//   Gov and City  Filter
        Route::get('GovernrateFilterr/{id}', 'App\Http\Controllers\WebsiteController@GovernrateFilter');
        Route::get('CityFilterr/{id}', 'App\Http\Controllers\WebsiteController@CityFilter');
        Route::get('CityShip/{id}', 'App\Http\Controllers\WebsiteController@CityShip');

Route::group(['middleware' => 'IFAuth:client'], function() {

  //My Account
        Route::get('MyAccountSite', 'App\Http\Controllers\WebsiteController@MyAccountSite');
        Route::get('MyProfileSite', 'App\Http\Controllers\WebsiteController@MyProfileSite');
        Route::post('UpdateAccount', 'App\Http\Controllers\WebsiteController@UpdateAccount');
        Route::post('UpdatePassword', 'App\Http\Controllers\WebsiteController@UpdatePassword');
        Route::post('UpdateAddress', 'App\Http\Controllers\WebsiteController@UpdateAddress');
        Route::get('DeleteMyAddress/{id}', 'App\Http\Controllers\WebsiteController@DeleteMyAddress');
        Route::post('EditMyAddress', 'App\Http\Controllers\WebsiteController@EditMyAddress');




    // Comment
      Route::post('AddComment', 'App\Http\Controllers\WebsiteController@AddComment');
      Route::post('EditComment', 'App\Http\Controllers\WebsiteController@EditComment');
      Route::get('DeleteComment/{id}', 'App\Http\Controllers\WebsiteController@DeleteComment');

    //Rate
       Route::post('AddRate', 'App\Http\Controllers\WebsiteController@AddRate');
      Route::post('EditRate', 'App\Http\Controllers\WebsiteController@EditRate');




});


// End View E-Commerce ===========================================================
});



Route::group(['middleware' => 'ViewAuthResturant'], function () {
//  View Resturant ===========================================================


//Auth
Route::group(['middleware' => 'IFNotAuth:client'], function() {
  Route::get('LoginResturantSite', 'App\Http\Controllers\ResturantController@LoginResturantSite');
    Route::post('PostLoginResturantSite', 'App\Http\Controllers\ResturantController@PostLoginResturantSite');
    Route::get('ForgotResturantSite', 'App\Http\Controllers\ResturantController@ForgotResturantSite');
    Route::post('PostForgoResturanttSite', 'App\Http\Controllers\ResturantController@PostForgoResturanttSite');
    Route::post('PostRegisterResturant', 'App\Http\Controllers\ResturantController@PostRegisterResturant');
    Route::post('PostResturantCodeSite', 'App\Http\Controllers\ResturantController@PostResturantCodeSite');
    Route::post('PostResturantResetPassword', 'App\Http\Controllers\ResturantController@PostResturantResetPassword');
});




Route::group(['middleware' => 'IFAuth:client'], function() {

  //My Account
        Route::get('MyAccountResturantSite', 'App\Http\Controllers\ResturantController@MyAccountResturantSite');
        Route::post('UpdateAccountResturant', 'App\Http\Controllers\ResturantController@UpdateAccountResturant');
        Route::post('UpdatePasswordResturant', 'App\Http\Controllers\ResturantController@UpdatePasswordResturant');
        Route::post('UpdateAddressResturant', 'App\Http\Controllers\ResturantController@UpdateAddressResturant');
        Route::get('DeleteMyAddressResturant/{id}', 'App\Http\Controllers\ResturantController@DeleteMyAddressResturant');
        Route::post('EditMyAddressResturant', 'App\Http\Controllers\ResturantController@EditMyAddressResturant');


    //Logout
  Route::get('LogoutResturantSite', 'App\Http\Controllers\ResturantController@LogoutResturantSite');



});





//Post Reservations
    Route::post('AddRReservations', 'App\Http\Controllers\ResturantController@AddRReservations');

    Route::group(['middleware' => 'BlogsResurantUse:client'], function() {
//BlogsResturantSite
  Route::get('BlogsResturantSite', 'App\Http\Controllers\ResturantController@BlogsResturantSite');
//BlogsResturantSiteDetails
 Route::get('BlogsResturantSiteDetails/{id}', 'App\Http\Controllers\ResturantController@BlogsResturantSiteDetails');
    });


        Route::group(['middleware' => 'ReviewsResurantUse:client'], function() {
//ReviewsResturantSite
  Route::get('ReviewsResturantSite', 'App\Http\Controllers\ResturantController@ReviewsResturantSite');
        });

        Route::group(['middleware' => 'GalleryResurantUse:client'], function() {
//GalleryResturantSite
  Route::get('GalleryResturantSite', 'App\Http\Controllers\ResturantController@GalleryResturantSite');
        });


        Route::group(['middleware' => 'TermsResurantUse:client'], function() {
//TermsResturantSite
  Route::get('TermsResturantSite', 'App\Http\Controllers\ResturantController@TermsResturantSite');
        });


        Route::group(['middleware' => 'PrivacyResurantUse:client'], function() {
//PrivacyResturantSite
  Route::get('PrivacyResturantSite', 'App\Http\Controllers\ResturantController@PrivacyResturantSite');
        });







//MenuSite
  Route::get('MenuSite', 'App\Http\Controllers\ResturantController@MenuSite');
//MenuFilter
  Route::get('MenuFilter/{id}', 'App\Http\Controllers\ResturantController@MenuFilter');


    Route::group(['middleware' => 'CartResurantUse:client'], function() {


//CartResturantSite
  Route::get('CartResturantSite', 'App\Http\Controllers\ResturantController@CartResturantSite');

//UpdateCartResturant
  Route::post('UpdateCartResturant', 'App\Http\Controllers\ResturantController@UpdateCartResturant');
//DeleteCartResturant
  Route::get('DeleteCartResturant/{id}', 'App\Http\Controllers\ResturantController@DeleteCartResturant');
//UpdateCuponCodeResturant
  Route::post('UpdateCuponCodeResturant', 'App\Http\Controllers\ResturantController@UpdateCuponCodeResturant');

//CheckoutResturantSite
  Route::get('CheckoutResturantSite', 'App\Http\Controllers\ResturantController@CheckoutResturantSite');
  Route::post('PlaceOrderResturant', 'App\Http\Controllers\ResturantController@PlaceOrderResturant');

//AddCartResturant
  Route::get('AddCartResturant', 'App\Http\Controllers\ResturantController@AddCartResturant');
  Route::get('MenuFilter/{id}/AddCartResturant', 'App\Http\Controllers\ResturantController@AddCartResturant');
//RemoveCartResturant
  Route::get('RemoveCartResturant', 'App\Http\Controllers\ResturantController@RemoveCartResturant');
  Route::get('MenuFilter/{id}/RemoveCartResturant', 'App\Http\Controllers\ResturantController@RemoveCartResturant');


    });

//  End View Resturant ===========================================================
});
   //Guest
       Route::get('CalendarMeet','App\Http\Controllers\AdminController@CalendarMeet');

       Route::get('GUESTLIST','App\Http\Controllers\AdminController@GUESTLIST');
      Route::get('GUESTLISTFilter','App\Http\Controllers\AdminController@GUESTLISTFilter');


Route::get('SEED', function () {

  Artisan::call('db:seed');


return back();

});


Route::get('DEBUG-SLIDERS', function () {
    $all = App\Models\Webslider::all();
    $website = App\Models\Webslider::where('Type', 0)->get();
    $mobile = App\Models\Webslider::where('Type', 1)->get();
    $nullType = App\Models\Webslider::whereNull('Type')->get();
    $withStatus = App\Models\Webslider::where('Status', 1)->get();

    return response()->json([
        'all_sliders' => $all->map(function($slider) {
            return [
                'id' => $slider->id,
                'Arabic_Title' => $slider->Arabic_Title,
                'English_Title' => $slider->English_Title,
                'Type' => $slider->Type,
                'Status' => $slider->Status ?? 'no_status_field',
                'Image' => $slider->Image,
                'created_at' => $slider->created_at
            ];
        }),
        'counts' => [
            'total' => $all->count(),
            'type_0' => $website->count(),
            'type_1' => $mobile->count(),
            'type_null' => $nullType->count(),
            'status_1' => $withStatus->count()
        ]
    ]);
});

Route::get('ADD-TEST-SLIDERS', function () {
    // Add test sliders if none exist
    $count = App\Models\Webslider::count();

    if ($count == 0) {
        App\Models\Webslider::create([
            'Arabic_Title' => 'شريحة اختبار 1',
            'English_Title' => 'Test Slide 1',
            'Arabic_Desc' => 'وصف الشريحة الأولى',
            'English_Desc' => 'Description for first slide',
            'Image' => 'https://via.placeholder.com/1920x800/007bff/ffffff?text=Slide+1',
            'Type' => 0,
            'Status' => 1
        ]);

        App\Models\Webslider::create([
            'Arabic_Title' => 'شريحة اختبار 2',
            'English_Title' => 'Test Slide 2',
            'Arabic_Desc' => 'وصف الشريحة الثانية',
            'English_Desc' => 'Description for second slide',
            'Image' => 'https://via.placeholder.com/1920x800/28a745/ffffff?text=Slide+2',
            'Type' => 0,
            'Status' => 1
        ]);

        App\Models\Webslider::create([
            'Arabic_Title' => 'شريحة اختبار 3',
            'English_Title' => 'Test Slide 3',
            'Arabic_Desc' => 'وصف الشريحة الثالثة',
            'English_Desc' => 'Description for third slide',
            'Image' => 'https://via.placeholder.com/1920x800/dc3545/ffffff?text=Slide+3',
            'Type' => 0,
            'Status' => 1
        ]);

        return response()->json([
            'message' => 'Test sliders created successfully',
            'count' => 3
        ]);
    } else {
        return response()->json([
            'message' => 'Sliders already exist',
            'count' => $count
        ]);
    }
});

Route::get('CHECK-VIEW', function () {
    $def = App\Models\CompanyData::orderBy('id','desc')->first();
    return response()->json([
        'current_view' => $def->View ?? 'not_set',
        'view_explanation' => [
            '0' => 'Welcome page',
            '1' => 'Website (WebsiteController@HomePage)',
            '2' => 'Restaurant (ResturantController@ResturantView)'
        ]
    ]);
});

Route::get('CHECK-IMAGES', function () {
    $sliders = App\Models\Webslider::all();
    $imageChecks = [];

    foreach($sliders as $slider) {
        $imagePath = $slider->Image;
        $fullUrl = URL::to($imagePath);
        $publicPath = public_path($imagePath);

        $imageChecks[] = [
            'id' => $slider->id,
            'title' => $slider->English_Title,
            'image_field' => $imagePath,
            'full_url' => $fullUrl,
            'public_path' => $publicPath,
            'file_exists' => file_exists($publicPath),
            'is_external_url' => str_starts_with($imagePath, 'http'),
        ];
    }

    return response()->json([
        'base_url' => url('/'),
        'public_path' => public_path(),
        'image_checks' => $imageChecks
    ]);
});

Route::get('FIX-SLIDER-IMAGES', function () {
    $sliders = App\Models\Webslider::all();
    $fixed = 0;

    foreach($sliders as $slider) {
        $imagePath = $slider->Image;

        // If image doesn't exist or is external URL that's broken, replace with placeholder
        if (str_starts_with($imagePath, 'http')) {
            // External URL - test if accessible
            $headers = @get_headers($imagePath);
            if (!$headers || strpos($headers[0], '200') === false) {
                // Broken external URL, replace with working placeholder
                $slider->Image = 'https://via.placeholder.com/1920x800/007bff/ffffff?text=Slider+' . $slider->id;
                $slider->save();
                $fixed++;
            }
        } else {
            // Local file - check if exists
            $publicPath = public_path($imagePath);
            if (!file_exists($publicPath)) {
                // File doesn't exist, replace with placeholder
                $slider->Image = 'https://via.placeholder.com/1920x800/007bff/ffffff?text=Slider+' . $slider->id;
                $slider->save();
                $fixed++;
            }
        }
    }

    return response()->json([
        'message' => "Fixed $fixed slider images",
        'total_sliders' => $sliders->count()
    ]);
});

Route::get('CLEAR', function () {

  Artisan::call('cache:clear');
  Artisan::call('view:clear');
  Artisan::call('route:clear');


return back();

});


Route::get('Truncate', function () {

    ReciptMaintaince::truncate();
PurchasesOrder::truncate();
SalesOrder::truncate();
Sales::truncate();
Quote::truncate();
Purchases::truncate();
Products::truncate();
ProductsQty::truncate();
ProductUnits::truncate();


    return back();
});




Route::get('Backup', function(){

return view('Backup');

});
Route::get('DeleteBackup','App\Http\Controllers\AdminController@DeleteBackup');

// === Admin Panel ===

//Language
    //Re Subscribtion
Route::get('ReSubscribtion', 'App\Http\Controllers\AdminController@ReSubscribtion');
Route::get('lang/{x}', 'App\Http\Controllers\LangController@Lang');
Route::get('ChangeLang', 'App\Http\Controllers\LangController@ChangeLang');

//Login and Register
Route::get('AdminLogin', 'App\Http\Controllers\AdminController@LoginPage');
Route::post('Login', 'App\Http\Controllers\AdminController@Login');
Route::get('Logout', 'App\Http\Controllers\AdminController@Logout');
Route::get('forgotpassword','App\Http\Controllers\AdminController@forgotpasswordPage');
Route::post('forgotpassword','App\Http\Controllers\AdminController@forgotpassword');
Route::get('reset/password/{token}','App\Http\Controllers\AdminController@reset_password');
Route::post('reset/password/{token}','App\Http\Controllers\AdminController@reset_password_final');

Config::set('auth.defines','admin');

Route::group(['middleware' =>'Admin:admin'], function () {
Route::group(['middleware' =>'auth:admin'], function() {




        Route::group(['middleware' => 'IFNotGuest:admin'], function() {

    //GusetListPrice
      Route::get('GusetListPrice','App\Http\Controllers\AdminController@GusetListPrice');
      Route::get('PriceListFilter','App\Http\Controllers\AdminController@PriceListFilter');

               });

Route::group(['middleware' =>'EXPIRED:admin'], function () {
    Route::group(['middleware' => 'IFGuest:admin'], function() {



    //Top Menu
        Route::get('OstAdmin','App\Http\Controllers\AdminController@OstAdmin');
        Route::get('BriefsAdmin','App\Http\Controllers\AdminController@BriefsAdmin');
        Route::get('StatisticsTotal','App\Http\Controllers\AdminController@StatisticsTotal');
        Route::get('StatisticsGraph','App\Http\Controllers\AdminController@StatisticsGraph');

        //Notifications
        Route::get('AllNotifucations','App\Http\Controllers\AdminController@AllNotifucations');
        Route::get('ReadNoti/{id}','App\Http\Controllers\AdminController@ReadNoti');
        Route::get('UnReadNoti/{id}','App\Http\Controllers\AdminController@UnReadNoti');
        Route::get('DeletNoti/{id}','App\Http\Controllers\AdminController@DeletNoti');



            //Profile
Route::get('Profile','App\Http\Controllers\AdminController@Profile');
Route::post('UpdateAdminProfile/{id?}','App\Http\Controllers\AdminController@UpdateAdminProfile');


        //Backup
        Route::get('serverDBBackup', 'App\Http\Controllers\StoresController@serverDBBackup');

        //Rabih / رابح
        Route::get('RabihEducation', 'App\Http\Controllers\AdminController@RabihEducation');
        Route::post('AddRabihEdu', 'App\Http\Controllers\AdminController@AddRabihEdu');
        Route::post('EditRabihEdu/{id}', 'App\Http\Controllers\AdminController@EditRabihEdu');
        Route::get('DeleteRabihEdu/{id}', 'App\Http\Controllers\AdminController@DeleteRabihEdu');

        Route::get('IntroView', 'App\Http\Controllers\AdminController@IntroView');
        Route::get('TermsView', 'App\Http\Controllers\AdminController@TermsView');
        Route::get('ReportIssue', 'App\Http\Controllers\AdminController@ReportIssue');
        Route::get('CreateNewChat', 'App\Http\Controllers\AdminController@CreateNewChat');
        Route::get('SolveIssue/{id}', 'App\Http\Controllers\AdminController@SolveIssue');
        Route::get('ChatIssue/{id}', 'App\Http\Controllers\AdminController@ChatIssue');
        Route::post('UploadChatImg', 'App\Http\Controllers\AdminController@UploadChatImg');
        Route::get('SendIssue', 'App\Http\Controllers\AdminController@SendIssue');
        Route::get('ChatIssue/1/SendIssue', 'App\Http\Controllers\AdminController@SendIssue');
        Route::get('ChatIssue/1/RefreshSendIssue', 'App\Http\Controllers\AdminController@RefreshSendIssue');

Route::get('BackupDB', function(){

  session()->flash('success',trans('admin.DBBackupSuccessfully'));
Artisan::call('backup:run', ['--only-db' => true]);

return redirect('Backup');

});

    //Filters
     Route::get('AllCustomers', 'App\Http\Controllers\AccountsController@AllCustomers');
     Route::get('AllCustomersJ/{id}', 'App\Http\Controllers\AccountsController@AllCustomersJ');
     Route::get('AllCustomersJPhone/{id}', 'App\Http\Controllers\AccountsController@AllCustomersJPhone');
     Route::get('AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('EditPurchasesOrder/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('TransferToPurchases/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('TransferToPurchasesRecived/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('EditPuechasesBill/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('TransferToPurchases/AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('TransferToPurchasesRecived/AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('TransferToPurchases/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('TransferToPurchasesRecived/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('AllCoins', 'App\Http\Controllers\AccountsController@AllCoins');
     Route::get('AllCoinsJ/{id}', 'App\Http\Controllers\AccountsController@AllCoinsJ');
     Route::get('AllBanksAccounts', 'App\Http\Controllers\AccountsController@AllBanksAccounts');
     Route::get('AllBanksAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllBanksAccountsJ');
     Route::get('AllCostss', 'App\Http\Controllers\AccountsController@AllCostss');
     Route::get('AllCostssJ/{id}', 'App\Http\Controllers\AccountsController@AllCostssJ');


     Route::get('AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('HoldSale/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('HoldSale/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('AllSubAccountsMsrofat', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofat');
     Route::get('AllSubAccountsMsrofatJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofatJ');
     Route::get('HoldSale/AllSubAccountsMsrofat', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofat');
     Route::get('HoldSale/AllSubAccountsMsrofatJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofatJ');


    Route::get('AllSubAccountsMwrden', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrden');
    Route::get('AllSubAccountsMwrdenJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrdenJ');
    Route::get('HoldSale/AllSubAccountsMwrden', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrden');
    Route::get('HoldSale/AllSubAccountsMwrdenJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrdenJ');

     Route::get('EditJournalizing/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditPayment_Voucher/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditReceipt_Voucher/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditOpening_Entries/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');

     Route::get('EditInvntory/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditInvntory/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('SettlementInvntory/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('SettlementInvntory/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('AllAccounts', 'App\Http\Controllers\AccountsController@AllAccounts');
     Route::get('EditJournalizing/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditPayment_Voucher/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditReceipt_Voucher/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditOpening_Entries/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');

     Route::get('AllMainAccounts', 'App\Http\Controllers\AccountsController@AllMainAccounts');
     Route::get('AllUsers', 'App\Http\Controllers\AccountsController@AllUsers');
     Route::get('AllUsersJ/{id}', 'App\Http\Controllers\AccountsController@AllUsersJ');
     Route::get('AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('TransferSureSafe/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditSafeTransfer/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('TransferSureSafe/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditSafeTransfer/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditPayment_Voucher/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditPayment_Voucher/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditReceipt_Voucher/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditReceipt_Voucher/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');


     Route::get('SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferToPurchases/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferToPurchasesRecived/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('ReturnSales/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditPayment_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditReceipt_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferSureSafe/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditSafeTransfer/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');

     Route::get('SurePayment_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('SureReceipt_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');

     Route::get('AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditPayment_Voucher/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditReceipt_Voucher/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditJournalizing/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditOpening_Entries/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');

     Route::get('AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditAttendance/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('HoldSale/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('ESBill/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditQuote/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditSalesOrder/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditAttendance/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('HoldSale/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('ESBill/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('EditQuote/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('EditSalesOrder/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');

     Route::get('AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('AllClientsJ/{id}', 'App\Http\Controllers\AccountsController@AllClientsJ');
     Route::get('AllClientsJPhone/{id}', 'App\Http\Controllers\AccountsController@AllClientsJPhone');


          Route::get('TicketEdit/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TicketEdit/1/AllClientsJ/{id}', 'App\Http\Controllers\AccountsController@AllClientsJ');
     Route::get('TicketEdit/1/AllClientsJPhone/{id}', 'App\Http\Controllers\AccountsController@AllClientsJPhone');
     Route::get('TicketEdit/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');

     Route::get('AllClientsFilterJ/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJ');
     Route::get('EditReciptMaintaince/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('HoldSale/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('ESBill/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesSO/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesExchange/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');

     Route::get('AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditReciptMaintaince/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesSO/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesExchange/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('HoldSale/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditQuote/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditSalesOrder/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('ESBill/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesExchange/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');


     Route::get('AllShips', 'App\Http\Controllers\AccountsController@AllShips');
     Route::get('AllShipsJ/{id}', 'App\Http\Controllers\AccountsController@AllShipsJ');
     Route::get('AllVend', 'App\Http\Controllers\AccountsController@AllVend');
     Route::get('AllVendJ/{id}', 'App\Http\Controllers\AccountsController@AllVendJ');
     Route::get('AllCli', 'App\Http\Controllers\AccountsController@AllCli');
     Route::get('AllCliJ/{id}', 'App\Http\Controllers\AccountsController@AllCliJ');

     Route::get('EditQuote/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesExchange/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('EditSalesOrder/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('MainAccountss/{id}', 'App\Http\Controllers\AccountsController@MainAccountss');


    Route::get('PurchPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToPurchases/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToPurchasesRecived/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('PurchOrdPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('QuotePrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesOrderPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint8/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint5/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('AddNewVendor/{co}/{name}/{price}', 'App\Http\Controllers\PurchasesController@AddNewVendor');
    Route::get('HoldSale/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
//===== Settings =========================================================================================================

            //User Premations
    Route::get('AdminsPremations', 'App\Http\Controllers\RoleController@AdminsPremationsPage');
    Route::post('AddPrem', 'App\Http\Controllers\RoleController@AddPrem');
    Route::post('EditPrem/{id}', 'App\Http\Controllers\RoleController@EditPrem');
    Route::get('DeletePrem/{id}', 'App\Http\Controllers\RoleController@DeletePrem');


          //Admins
Route::get('Admins', 'App\Http\Controllers\AdminController@AdminsPage');
Route::post('AddAdmin', 'App\Http\Controllers\AdminController@AddAdmin');
Route::post('EditAdmin/{id}', 'App\Http\Controllers\AdminController@EditAdmin');
Route::get('DeleteAdmin/{id}', 'App\Http\Controllers\AdminController@DeleteAdmin');
Route::get('UnHide/{id}', 'App\Http\Controllers\AdminController@UnHide');
Route::get('Hide/{id}', 'App\Http\Controllers\AdminController@Hide');

        //DeleteMoves
        Route::get('DeleteMoves', 'App\Http\Controllers\AdminController@DeleteMoves');
Route::post('PostDeleteMoves', 'App\Http\Controllers\AdminController@PostDeleteMoves');

            //Default_Data
            Route::get('Default_Data', 'App\Http\Controllers\AdminController@Default_DataPage');
            Route::post('AddDefaultCompany', 'App\Http\Controllers\AdminController@AddDefaultCompany');
            Route::get('AddDefaultCompanyFirst', 'App\Http\Controllers\AdminController@AddDefaultCompanyFirst');
            Route::post('AddDefaultAccount', 'App\Http\Controllers\AdminController@AddDefaultAccount');
            Route::get('AddDefaultAccountsFirst', 'App\Http\Controllers\AdminController@AddDefaultAccountsFirst');
            Route::post('AddDefaultStore', 'App\Http\Controllers\AdminController@AddDefaultStore');
            Route::get('AddDefaultStoreFirst', 'App\Http\Controllers\AdminController@AddDefaultStoreFirst');
            Route::post('AddDefaultCrm', 'App\Http\Controllers\AdminController@AddDefaultCrm');
            Route::get('AddDefaultCrmFirst', 'App\Http\Controllers\AdminController@AddDefaultCrmFirst');
            Route::post('AddDefaultPurchases', 'App\Http\Controllers\AdminController@AddDefaultPurchases');
            Route::get('AddDefaultPurchasesFirst', 'App\Http\Controllers\AdminController@AddDefaultPurchasesFirst');
            Route::post('AddDefaultSales', 'App\Http\Controllers\AdminController@AddDefaultSales');
            Route::get('AddDefaultSalesFirst', 'App\Http\Controllers\AdminController@AddDefaultSalesFirst');
            Route::post('AddDefaultShowHide', 'App\Http\Controllers\AdminController@AddDefaultShowHide');
            Route::get('AddDefaultShowHideFirst', 'App\Http\Controllers\AdminController@AddDefaultShowHideFirst');
            Route::post('AddDefaultMaintaince', 'App\Http\Controllers\AdminController@AddDefaultMaintaince');
            Route::get('AddDefaultMaintainceFirst', 'App\Http\Controllers\AdminController@AddDefaultMaintainceFirst');
            Route::post('AddDefaultManufacture', 'App\Http\Controllers\AdminController@AddDefaultManufacture');
            Route::get('AddDefaultManufactureFirst', 'App\Http\Controllers\AdminController@AddDefaultManufactureFirst');
            Route::post('AddDefaultShipping', 'App\Http\Controllers\AdminController@AddDefaultShipping');
            Route::get('AddDefaultShippingFirst', 'App\Http\Controllers\AdminController@AddDefaultShippingFirst');
        Route::post('AddDefaultCustomPrint', 'App\Http\Controllers\AdminController@AddDefaultCustomPrint');
            Route::get('AddDefaultCustomPrintFirst', 'App\Http\Controllers\AdminController@AddDefaultCustomPrintFirst');



        //Modules_Settings
          Route::get('Modules_Settings', 'App\Http\Controllers\AdminController@Modules_SettingsPage');
          Route::get('AddDefaultModulesFirst', 'App\Http\Controllers\AdminController@AddDefaultModulesFirst');
          Route::get('AddDefaultModulesNumFirst', 'App\Http\Controllers\AdminController@AddDefaultModulesNumFirst');

        Route::post('AddDefaultIntro', 'App\Http\Controllers\AdminController@AddDefaultIntro');
          Route::get('AddDefaultIntroFirst', 'App\Http\Controllers\AdminController@AddDefaultIntroFirst');


          Route::post('AddDefaultModules', 'App\Http\Controllers\AdminController@AddDefaultModules');
          Route::post('AddDefaultModulesNum', 'App\Http\Controllers\AdminController@AddDefaultModulesNum');
        Route::post('AddDefaultReportSettings', 'App\Http\Controllers\AdminController@AddDefaultReportSettings');
            Route::get('AddDefaultReportSettingsFirst', 'App\Http\Controllers\AdminController@AddDefaultReportSettingsFirst');
            Route::post('AddDefaultPackagesSettings', 'App\Http\Controllers\AdminController@AddDefaultPackagesSettings');
            Route::get('DeletePackage/{id}', 'App\Http\Controllers\AdminController@DeletePackage');

        //Translate
          Route::get('Translate', 'App\Http\Controllers\AdminController@TranslatePage');
          Route::post('AddTranslate', 'App\Http\Controllers\AdminController@AddTranslate');
          Route::post('EditTranslate/{id}', 'App\Http\Controllers\AdminController@EditTranslate');
          Route::get('DeleteTranslate/{id}', 'App\Http\Controllers\AdminController@DeleteTranslate');

//==== End Settings =======================================================================================================

//=====  Accounts ==========================================================================================================
        //QR
            Route::get('QR', 'App\Http\Controllers\AdminController@QRPage');

    //Accounting Manual
    Route::get('AccountingManual', 'App\Http\Controllers\AccountsController@AccountingManualPage');
    Route::post('AddAccount', 'App\Http\Controllers\AccountsController@AddAccount');
    Route::post('EditAccount', 'App\Http\Controllers\AccountsController@EditAccount');
    Route::get('DeleteAccount/{id}', 'App\Http\Controllers\AccountsController@DeleteAccount');


                //Cost Centers
Route::get('CostCenters', 'App\Http\Controllers\AccountsController@CostCentersPage');
Route::post('AddCostCenters', 'App\Http\Controllers\AccountsController@AddCostCenters');
Route::post('EditCostCenters/{id}', 'App\Http\Controllers\AccountsController@EditCostCenters');
Route::get('DeleteCostCenters/{id}', 'App\Http\Controllers\AccountsController@DeleteCostCenters');


                    //Coins
Route::get('Coins', 'App\Http\Controllers\AccountsController@CoinsPage');
Route::post('AddCoins', 'App\Http\Controllers\AccountsController@AddCoins');
Route::post('EditCoins/{id}', 'App\Http\Controllers\AccountsController@EditCoins');
Route::get('DeleteCoins/{id}', 'App\Http\Controllers\AccountsController@DeleteCoins');


   //Checks_Type
Route::get('Checks_Type', 'App\Http\Controllers\AccountsController@Checks_TypePage');
Route::post('AddChecks_Type', 'App\Http\Controllers\AccountsController@AddChecks_Type');
Route::post('EditChecks_Type/{id}', 'App\Http\Controllers\AccountsController@EditChecks_Type');
Route::get('DeleteChecks_Type/{id}', 'App\Http\Controllers\AccountsController@DeleteChecks_Type');


//Journalizing
    Route::get('Journalizing', 'App\Http\Controllers\AccountsController@JournalizingPage');
    Route::get('JournalizingSechduleFilter', 'App\Http\Controllers\AccountsController@JournalizingSechduleFilter');
    Route::post('AddJournalizing', 'App\Http\Controllers\AccountsController@AddJournalizing');
    Route::get('JournalizingPrint/{id}', 'App\Http\Controllers\AccountsController@JournalizingPrint');
    Route::get('SureJournalizing/{id}', 'App\Http\Controllers\AccountsController@SureJournalizing');
    Route::post('PostSureJournalizing', 'App\Http\Controllers\AccountsController@PostSureJournalizing');


    //Receipt_Voucher
    Route::get('Receipt_Voucher', 'App\Http\Controllers\AccountsController@Receipt_VoucherPage');
    Route::get('ReceiptVoucherSechduleFilter', 'App\Http\Controllers\AccountsController@ReceiptVoucherSechduleFilter');
    Route::post('AddReceipt_Voucher', 'App\Http\Controllers\AccountsController@AddReceipt_Voucher');
    Route::get('Receipt_VoucherPrint/{id}', 'App\Http\Controllers\AccountsController@Receipt_VoucherPrint');
Route::get('SureReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@SureReceipt_Voucher');
            Route::post('PostSureReceipt_Voucher', 'App\Http\Controllers\AccountsController@PostSureReceipt_Voucher');

    //Payment_Voucher
    Route::get('Payment_Voucher', 'App\Http\Controllers\AccountsController@Payment_VoucherPage');
    Route::get('PaymentVoucherSechduleFilter', 'App\Http\Controllers\AccountsController@PaymentVoucherSechduleFilter');
    Route::post('AddPayment_Voucher', 'App\Http\Controllers\AccountsController@AddPayment_Voucher');
    Route::get('Payment_VoucherPrint/{id}', 'App\Http\Controllers\AccountsController@Payment_VoucherPrint');
Route::get('SurePayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@SurePayment_Voucher');
            Route::post('PostSurePayment_Voucher', 'App\Http\Controllers\AccountsController@PostSurePayment_Voucher');

//Opening Entries
       Route::get('OpeningEntries', 'App\Http\Controllers\AccountsController@OpeningEntriesPage');
       Route::get('OpeningEntriesSechduleFilter', 'App\Http\Controllers\AccountsController@OpeningEntriesSechduleFilter');
       Route::post('AddOpeningEntries', 'App\Http\Controllers\AccountsController@AddOpeningEntries');
       Route::get('Opening_EntriesPrint/{id}', 'App\Http\Controllers\AccountsController@Opening_EntriesPrint');
Route::get('SureOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@SureOpening_Entries');
            Route::post('PostSureOpeningEntries', 'App\Http\Controllers\AccountsController@PostSureOpeningEntries');

//Exporting_Checks
         Route::get('Exporting_Checks', 'App\Http\Controllers\AccountsController@Exporting_ChecksPage');
         Route::get('ExportingChecksSechduleFilter', 'App\Http\Controllers\AccountsController@ExportingChecksSechduleFilter');
         Route::post('AddExporting_Checks', 'App\Http\Controllers\AccountsController@AddExporting_Checks');
         Route::post('EditExporting_Checks/{id}', 'App\Http\Controllers\AccountsController@EditExporting_Checks');
         Route::post('ReasonExportChecks', 'App\Http\Controllers\AccountsController@ReasonExportChecks');
         Route::post('TransExportingChecks', 'App\Http\Controllers\AccountsController@TransExportingChecks');
         Route::get('DeleteExportingChecks/{id}', 'App\Http\Controllers\AccountsController@DeleteExportingChecks');
         Route::get('PrintOutcomChecks/{id}', 'App\Http\Controllers\AccountsController@PrintOutcomChecks');
         Route::post('PayExportingChecks', 'App\Http\Controllers\AccountsController@PayExportingChecks');


//Incoming_checks
         Route::get('Incoming_checks', 'App\Http\Controllers\AccountsController@Incoming_checksPage');
         Route::get('IncomingchecksSechduleFilter', 'App\Http\Controllers\AccountsController@IncomingchecksSechduleFilter');
         Route::post('AddIncoming_checks', 'App\Http\Controllers\AccountsController@AddIncoming_checks');
         Route::post('EditIncoming_checks/{id}', 'App\Http\Controllers\AccountsController@EditIncoming_checks');
         Route::post('ReasonIncoming_checks', 'App\Http\Controllers\AccountsController@ReasonIncoming_checks');
         Route::post('TransIncoming_checks', 'App\Http\Controllers\AccountsController@TransIncoming_checks');
         Route::get('DeleteIncoming_checks/{id}', 'App\Http\Controllers\AccountsController@DeleteIncoming_checks');
         Route::get('PrintIncomChecks/{id}', 'App\Http\Controllers\AccountsController@PrintIncomChecks');
         Route::post('PayIncomingChecks', 'App\Http\Controllers\AccountsController@PayIncomingChecks');

    //Insurance_Paper
        Route::get('Insurance_Paper', 'App\Http\Controllers\AccountsController@Insurance_PaperPage');
        Route::get('InsurancePaperSechduleFilter', 'App\Http\Controllers\AccountsController@InsurancePaperSechduleFilter');
        Route::post('AddInsurancePaper', 'App\Http\Controllers\AccountsController@AddInsurancePaper');
        Route::get('DeleteInsurancePaper/{id}', 'App\Http\Controllers\AccountsController@DeleteInsurancePaper');
        Route::post('RecivedInurance/{id}', 'App\Http\Controllers\AccountsController@RecivedInurance');


//Safes and Banks
Route::get('Safes_Banks', 'App\Http\Controllers\AccountsController@Safes_BanksPage');
Route::post('AddSafes_Banks', 'App\Http\Controllers\AccountsController@AddSafes_Banks');
Route::post('EditSafes_Banks/{id}', 'App\Http\Controllers\AccountsController@EditSafes_Banks');
Route::get('DeleteSafes_Banks/{id}', 'App\Http\Controllers\AccountsController@DeleteSafes_Banks');



     //Journalizing Sechdule
     Route::get('JournalizingSechdule', 'App\Http\Controllers\AccountsController@JournalizingSechdule');
     Route::get('EditJournalizing/{id}', 'App\Http\Controllers\AccountsController@EditJournalizing');
     Route::get('DeleteJournalizing/{id}', 'App\Http\Controllers\AccountsController@DeleteJournalizing');
     Route::post('PostEditJournalizing', 'App\Http\Controllers\AccountsController@PostEditJournalizing');

      //Receipt Voucher Sechdule
     Route::get('Receipt_VoucherSechdule', 'App\Http\Controllers\AccountsController@Receipt_VoucherSechdule');
     Route::get('EditReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@EditReceipt_Voucher');
     Route::get('DeleteReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@DeleteReceipt_Voucher');
     Route::post('PostEditReceipt_Voucher', 'App\Http\Controllers\AccountsController@PostEditReceipt_Voucher');

      //Payment Voucher Sechdule
     Route::get('Payment_VoucherSechdule', 'App\Http\Controllers\AccountsController@Payment_VoucherSechdule');
     Route::get('EditPayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@EditPayment_Voucher');
     Route::get('DeletePayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@DeletePayment_Voucher');
     Route::post('PostEditPayment_Voucher', 'App\Http\Controllers\AccountsController@PostEditPayment_Voucher');

          //Opening Entries Sechdule
     Route::get('Opening_EntriesSechdule', 'App\Http\Controllers\AccountsController@Opening_EntriesSechdule');
     Route::get('EditOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@EditOpening_Entries');
     Route::get('DeleteOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@DeleteOpening_Entries');
     Route::post('PostEditOpeningEntries', 'App\Http\Controllers\AccountsController@PostEditOpening_Entries');
     //SafesTransfer
            Route::get('SafesTransfer', 'App\Http\Controllers\AccountsController@SafesTransferPage');
            Route::get('TransferSureSafe/{id}', 'App\Http\Controllers\AccountsController@TransferSureSafe');
            Route::get('RefusedSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@RefusedSafeTransfer');
            Route::get('EditSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@EditSafeTransfer');
            Route::post('AddSafeTransfer', 'App\Http\Controllers\AccountsController@AddSafeTransfer');
            Route::post('SureSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@SureSafeTransfer');
            Route::post('PostEditSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@PostEditSafeTransfer');
            Route::get('SafesTransferSechdule', 'App\Http\Controllers\AccountsController@SafesTransferSechdulePage');
            Route::get('TransSafePrint/{id}', 'App\Http\Controllers\AccountsController@TransSafePrint');


//Assets
       Route::get('Assets', 'App\Http\Controllers\AccountsController@AssetsPage');
       Route::get('AssetsSechduleFilter', 'App\Http\Controllers\AccountsController@AssetsSechduleFilter');
       Route::post('AddAssets', 'App\Http\Controllers\AccountsController@AddAssets');
       Route::post('AssetSale', 'App\Http\Controllers\AccountsController@AssetSale');
       Route::get('DeleteAssets/{id}', 'App\Http\Controllers\AccountsController@DeleteAssets');
       Route::get('AllMainAssetsAccounts', 'App\Http\Controllers\AccountsController@AllMainAssetsAccounts');
       Route::get('AllMainAssetsAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllMainAssetsAccountsJ');
       Route::get('AllAccountsExpenses', 'App\Http\Controllers\AccountsController@AllAccountsExpenses');
       Route::get('AllAccountsExpensesJ/{id}', 'App\Http\Controllers\AccountsController@AllAccountsExpensesJ');
       Route::get('AllAccountsComplex', 'App\Http\Controllers\AccountsController@AllAccountsComplex');
       Route::get('AllAccountsComplexJ/{id}', 'App\Http\Controllers\AccountsController@AllAccountsComplexJ');

//Assets
       Route::get('AssetExpenses', 'App\Http\Controllers\AccountsController@AssetExpensesPage');
       Route::post('AddAssetsExpenses', 'App\Http\Controllers\AccountsController@AddAssetsExpenses');

//==== End Accounts ==========================================================================================================


//=====  Accounts Reports  =================================================================================================
  //General_Daily
  Route::get('General_Daily', 'App\Http\Controllers\AccountReportsController@General_DailyPage');
  Route::get('GenralDailyFilter', 'App\Http\Controllers\AccountReportsController@GenralDailyFilter');
  Route::get('GenralDailyFilterBond', 'App\Http\Controllers\AccountReportsController@GenralDailyFilterBond');
  Route::get('GeneralDailyFilterTwo', 'App\Http\Controllers\AccountReportsController@GeneralDailyFilterTwo');


  //Trial_Balance
  Route::get('Trial_Balance', 'App\Http\Controllers\AccountReportsController@Trial_BalancePage');
  Route::get('FilterTrial_Balance', 'App\Http\Controllers\AccountReportsController@FilterTrial_Balance');
  Route::get('TrialBalanceFilterTwo', 'App\Http\Controllers\AccountReportsController@TrialBalanceFilterTwo');


  //Account_Balances
  Route::get('Account_Balances', 'App\Http\Controllers\AccountReportsController@Account_BalancesPage');
  Route::get('FilterAccount_Balances', 'App\Http\Controllers\AccountReportsController@FilterAccount_Balances');
  Route::get('AccountBalancesFilterTwo', 'App\Http\Controllers\AccountReportsController@AccountBalancesFilterTwo');

  //Ledger
  Route::get('Ledger', 'App\Http\Controllers\AccountReportsController@LedgerPage');
  Route::get('FilterLedger', 'App\Http\Controllers\AccountReportsController@FilterLedger');
  Route::post('FilterPrintLedger', 'App\Http\Controllers\AccountReportsController@FilterPrintLedger');
  Route::get('LedgerFilterTwo', 'App\Http\Controllers\AccountReportsController@LedgerFilterTwo');

  //Safe_Bank_Statement
  Route::get('Safe_Bank_Statement', 'App\Http\Controllers\AccountReportsController@Safe_Bank_StatementPage');
  Route::get('FilterSafe_Bank_Statement', 'App\Http\Controllers\AccountReportsController@FilterSafe_Bank_Statement');
          Route::get('SafeBankStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@SafeBankStatementFilterTwo');

  //Customer_Balances
  Route::get('Customer_Balances', 'App\Http\Controllers\AccountReportsController@Customer_BalancesPage');
  Route::get('FilterCustomer_Balances', 'App\Http\Controllers\AccountReportsController@FilterCustomer_Balances');
Route::get('CustomerBalancesFilterTwo', 'App\Http\Controllers\AccountReportsController@CustomerBalancesFilterTwo');
  //Vendor_Account_Statement
  Route::get('Vendor_Account_Statement', 'App\Http\Controllers\AccountReportsController@Vendor_Account_StatementPage');
  Route::get('FilterVendor_Account_Statement', 'App\Http\Controllers\AccountReportsController@FilterVendor_Account_Statement');
Route::get('VendorAccountStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@VendorAccountStatementFilterTwo');

  //Customer_Account_Statement
  Route::get('Customer_Account_Statement', 'App\Http\Controllers\AccountReportsController@Customer_Account_StatementPage');
  Route::get('FilterCustomer_Account_Statement', 'App\Http\Controllers\AccountReportsController@FilterCustomer_Account_Statement');
Route::get('CustomerAccountStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@CustomerAccountStatementFilterTwo');
  //Cost_Centers_Report
  Route::get('Cost_Centers_Report', 'App\Http\Controllers\AccountReportsController@Cost_Centers_ReportPage');
  Route::get('Cost_Centers_ReportFilter', 'App\Http\Controllers\AccountReportsController@Cost_Centers_ReportFilter');


  //Checks_Reports
  Route::get('Checks_Reports', 'App\Http\Controllers\AccountReportsController@Checks_ReportsPage');
  Route::get('Checks_ReportsFilter', 'App\Http\Controllers\AccountReportsController@Checks_ReportsFilter');
Route::get('ChecksReportsFilterTwo', 'App\Http\Controllers\AccountReportsController@ChecksReportsFilterTwo');

//InsurancePaperReport
          Route::get('InsurancePaperReport', 'App\Http\Controllers\AccountReportsController@InsurancePaperReport');
Route::get('InsurancePaperReportFilter', 'App\Http\Controllers\AccountReportsController@InsurancePaperReportFilter');

  //Incom_List
  Route::get('Incom_List', 'App\Http\Controllers\AccountReportsController@Incom_ListPage');
Route::get('FilterIncom_ListNew', 'App\Http\Controllers\AccountReportsController@FilterIncom_ListNew');
Route::get('FilterIncom_ListErydat', 'App\Http\Controllers\AccountReportsController@FilterIncom_ListErydat');
Route::get('FilterIncom_ListTaklfa', 'App\Http\Controllers\AccountReportsController@FilterIncom_ListTaklfa');
Route::get('FilterIncom_ListMasrofat', 'App\Http\Controllers\AccountReportsController@FilterIncom_ListMasrofat');
  //Financial_Center
  Route::get('Financial_Center', 'App\Http\Controllers\AccountReportsController@Financial_CenterPage');
  Route::get('FilterFinancial_CenterNew', 'App\Http\Controllers\AccountReportsController@FilterFinancial_CenterNew');
  Route::get('FilterFinancial_CenterAsoul', 'App\Http\Controllers\AccountReportsController@FilterFinancial_CenterAsoul');
  Route::get('FilterFinancial_CenterKhsoum', 'App\Http\Controllers\AccountReportsController@FilterFinancial_CenterKhsoum');
  Route::get('FilterFinancial_CenterHkook', 'App\Http\Controllers\AccountReportsController@FilterFinancial_CenterHkook');

  //Safes_Balances
  Route::get('Safes_Balances', 'App\Http\Controllers\AccountReportsController@Safes_BalancesPage');
  Route::get('FilterSafes_Balances', 'App\Http\Controllers\AccountReportsController@FilterSafes_Balances');

//Fixed_Assets_Report
          Route::get('Fixed_Assets_Report', 'App\Http\Controllers\AccountReportsController@Fixed_Assets_Report');
  Route::get('Fixed_Assets_ReportFilterTwo', 'App\Http\Controllers\AccountReportsController@Fixed_Assets_ReportFilterTwo');

//==== End Accounts Reports=================================================================================================


// === Stores  ========================================================================================================

    //Stores
Route::get('Stores', 'App\Http\Controllers\StoresController@StoresPage');
Route::post('AddStores', 'App\Http\Controllers\StoresController@AddStores');
Route::post('EditStores/{id}', 'App\Http\Controllers\StoresController@EditStores');
Route::get('DeleteStores/{id}', 'App\Http\Controllers\StoresController@DeleteStores');


                        //Measurement_Units
Route::get('Measurement_Units', 'App\Http\Controllers\StoresController@Measurement_UnitsPage');
Route::post('AddMeasurement_Units', 'App\Http\Controllers\StoresController@AddMeasurement_Units');
Route::post('EditMeasurement_Units/{id}', 'App\Http\Controllers\StoresController@EditMeasurement_Units');
Route::get('DeleteMeasurement_Units/{id}', 'App\Http\Controllers\StoresController@DeleteMeasurement_Units');



                    //Manufacture
Route::get('Manufacture', 'App\Http\Controllers\StoresController@ManufacturePage');
Route::post('AddManufacture', 'App\Http\Controllers\StoresController@AddManufacture');
Route::post('EditManufacture/{id}', 'App\Http\Controllers\StoresController@EditManufacture');
Route::get('DeleteManufacture/{id}', 'App\Http\Controllers\StoresController@DeleteManufacture');


                       //Virables
Route::get('Virables', 'App\Http\Controllers\StoresController@VirablesPage');
Route::post('AddVirables', 'App\Http\Controllers\StoresController@AddVirables');
Route::post('EditVirables/{id}', 'App\Http\Controllers\StoresController@EditVirables');
Route::get('DeleteVirables/{id}', 'App\Http\Controllers\StoresController@DeleteVirables');

                           //Sub_Virables
Route::get('Sub_Virables/{id}', 'App\Http\Controllers\StoresController@Sub_VirablesPage');
Route::post('AddSub_Virables', 'App\Http\Controllers\StoresController@AddSub_Virables');
Route::post('EditSub_Virables/{id}', 'App\Http\Controllers\StoresController@EditSub_Virables');
Route::get('DeleteSub_Virables/{id}', 'App\Http\Controllers\StoresController@DeleteSub_Virables');


                       //Taxes
Route::get('Taxes', 'App\Http\Controllers\StoresController@TaxesPage');
Route::post('AddTaxes', 'App\Http\Controllers\StoresController@AddTaxes');
Route::post('EditTaxes/{id}', 'App\Http\Controllers\StoresController@EditTaxes');
Route::get('DeleteTaxes/{id}', 'App\Http\Controllers\StoresController@DeleteTaxes');


                           //SubscribeTypes
Route::get('SubscribeTypes', 'App\Http\Controllers\StoresController@SubscribeTypesPage');
Route::post('AddSubscribeTypes', 'App\Http\Controllers\StoresController@AddSubscribeTypes');
Route::post('EditSubscribeTypes/{id}', 'App\Http\Controllers\StoresController@EditSubscribeTypes');
Route::get('DeleteSubscribeTypes/{id}', 'App\Http\Controllers\StoresController@DeleteSubscribeTypes');


                       //Items_Groups
Route::get('Items_Groups', 'App\Http\Controllers\StoresController@Items_GroupsPage');
Route::post('AddItems_Groups', 'App\Http\Controllers\StoresController@AddItems_Groups');
Route::post('EditItems_Groups/{id}', 'App\Http\Controllers\StoresController@EditItems_Groups');
Route::get('DeleteItems_Groups/{id}', 'App\Http\Controllers\StoresController@DeleteItems_Groups');


    //Products
    Route::get('Add_Items', 'App\Http\Controllers\StoresController@Add_ItemsPage');
    Route::post('PostAddProduct', 'App\Http\Controllers\StoresController@PostAddProduct');
    Route::get('UnitNameFilter/{id}', 'App\Http\Controllers\StoresController@UnitNameFilter');
    Route::get('AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
    Route::get('VariableAggregateFilter', 'App\Http\Controllers\StoresController@VariableAggregateFilter');
    Route::get('VASelectFilterr', 'App\Http\Controllers\StoresController@VASelectFilterr');
    Route::get('VASubSelectFilterr', 'App\Http\Controllers\StoresController@VASubSelectFilterr');
    Route::get('UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
    Route::get('UnitNameCodeFilterr/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilterr');
    Route::get('VOneFilter', 'App\Http\Controllers\StoresController@VOneFilter');
    Route::get('VTwoFilter', 'App\Http\Controllers\StoresController@VTwoFilter');

        Route::get('EditItems/{id}/VOneFilter', 'App\Http\Controllers\StoresController@VOneFilter');
    Route::get('EditItems/{id}/VTwoFilter', 'App\Http\Controllers\StoresController@VTwoFilter');
    Route::get('AddCheckName', 'App\Http\Controllers\StoresController@AddCheckName');
    Route::get('FilterPSechdule', 'App\Http\Controllers\StoresController@FilterPSechdule');
    Route::get('AddNewProduct/{Name}/{EnName}/{P_Type}/{Brand}/{Group}/{unit}/{Rate}/{Barcode}/{Price}/{Price_Two}/{Price_Three}', 'App\Http\Controllers\StoresController@AddNewProduct');
     Route::get('TaxPriceFilter', 'App\Http\Controllers\StoresController@TaxPriceFilter');

    //Product Schdule
       Route::get('Products_Sechdule', 'App\Http\Controllers\StoresController@Products_SechdulePage');
       Route::post('PostEditAV', 'App\Http\Controllers\StoresController@PostEditAV');
       Route::post('PostEditAddittionAV', 'App\Http\Controllers\StoresController@PostEditAddittionAV');

       Route::post('PostEditQTYPriceAV', 'App\Http\Controllers\StoresController@PostEditQTYPriceAV');
       Route::get('DeleteVAPro/{id}', 'App\Http\Controllers\StoresController@DeleteVAPro');
       Route::get('DeleteVAProQty/{id}', 'App\Http\Controllers\StoresController@DeleteVAProQty');
       Route::get('DeleteVAPrice/{id}', 'App\Http\Controllers\StoresController@DeleteVAPrice');

       Route::get('EditItems/{id}', 'App\Http\Controllers\StoresController@EditItems');
       Route::get('UnActiveItem/{id}', 'App\Http\Controllers\StoresController@UnActiveItem');
       Route::get('ActiveItem/{id}', 'App\Http\Controllers\StoresController@ActiveItem');
       Route::get('DeleteItem/{id}', 'App\Http\Controllers\StoresController@DeleteItem');
       Route::get('DelSubImage/{id}', 'App\Http\Controllers\StoresController@DelSubImage');
       Route::get('EditItems/UnitNameFilter/{id}', 'App\Http\Controllers\StoresController@UnitNameFilter');
       Route::get('EditItems/AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
       Route::get('EditItems/{id}/VariableAggregateFilter', 'App\Http\Controllers\StoresController@VariableAggregateFilter');
       Route::get('EditItems/{id}/VASelectFilterr', 'App\Http\Controllers\StoresController@VASelectFilterr');
       Route::get('EditItems/{id}/AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
       Route::get('EditItems/UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
       Route::get('EditItems/UnitNameCodeFilterr/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilterr');
       Route::post('PostEditProduct/{id}', 'App\Http\Controllers\StoresController@PostEditProduct');
       Route::post('MultiDeleteVira', 'App\Http\Controllers\StoresController@MultiDeleteVira');
       Route::get('SerialProductsQtyFilter', 'App\Http\Controllers\StoresController@SerialProductsQtyFilter');
       Route::get('DeleteSerialQty/{id}', 'App\Http\Controllers\StoresController@DeleteSerialQty');
       Route::get('EditSerialQty', 'App\Http\Controllers\StoresController@EditSerialQty');


    //Start Period Products
     Route::get('StartPeriodProducts', 'App\Http\Controllers\StoresController@StartPeriodProductsPage');
     Route::post('AddStartPeriod', 'App\Http\Controllers\StoresController@AddStartPeriod');
     Route::get('StartProductsFilter/{store}', 'App\Http\Controllers\StoresController@StartProductsFilter');
     Route::get('ViraFilter/{id}', 'App\Http\Controllers\StoresController@ViraFilter');
     Route::get('ViraTwoFilter/{id}', 'App\Http\Controllers\StoresController@ViraTwoFilter');
     Route::get('ViraName/{id}', 'App\Http\Controllers\StoresController@ViraName');

       //Start Period Sechdule
       Route::get('StartPeriodSechdule', 'App\Http\Controllers\StoresController@StartPeriodSechdulePage');
       Route::get('EditStartPeriod/{id}', 'App\Http\Controllers\StoresController@EditStartPeriod');
       Route::get('DeleteStartPeriod/{id}', 'App\Http\Controllers\StoresController@DeleteStartPeriod');
       Route::post('PostEditStartPeriod/{id}', 'App\Http\Controllers\StoresController@PostEditStartPeriod');
       Route::get('EditStartPeriod/StartProductsFilter/{store}', 'App\Http\Controllers\StoresController@StartProductsFilter');
       Route::get('EditStartPeriod/ViraFilter/{id}', 'App\Http\Controllers\StoresController@ViraFilter');
       Route::get('EditStartPeriod/ViraTwoFilter/{id}', 'App\Http\Controllers\StoresController@ViraTwoFilter');
       Route::get('EditStartPeriod/ViraName/{id}', 'App\Http\Controllers\StoresController@ViraName');
       Route::get('EditStartPeriod/UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
       Route::get('StartPeriodPrint/{id}', 'App\Http\Controllers\StoresController@StartPeriodPrint');
       Route::get('MoreProDetailsStart', 'App\Http\Controllers\StoresController@MoreProDetailsStart');

       //Inventory
       Route::get('Inventory', 'App\Http\Controllers\StoresController@InventoryPage');
       Route::get('InventoryFilter', 'App\Http\Controllers\StoresController@InventoryFilter');
       Route::Post('AddInventory', 'App\Http\Controllers\StoresController@AddInventory');
       Route::get('UnitNameCodeInventoryFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');
        Route::get('UnitNameCodeInventoryFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
        Route::get('UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');

    //Inventory Sechdule
       Route::get('Inventory_Sechdule', 'App\Http\Controllers\StoresController@Inventory_SechdulePage');
       Route::get('Settlement_Sechdule', 'App\Http\Controllers\StoresController@Settlement_SechdulePage');
       Route::get('DeleteInventory/{id}', 'App\Http\Controllers\StoresController@DeleteInventory');
       Route::get('EditInvntory/{id}', 'App\Http\Controllers\StoresController@EditInvntory');
       Route::get('SettlementInvntory/{id}', 'App\Http\Controllers\StoresController@SettlementInvntory');
       Route::get('EditInvntory/{id}/InventoryFilter', 'App\Http\Controllers\StoresController@InventoryFilter');

    Route::get('EditInvntory/UnitNameCodeInventoryFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');

    Route::get('EditInvntory/UnitNameCodeInventoryFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
    Route::get('EditInvntory/UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');

     Route::Post('PostEditInventory/{id}', 'App\Http\Controllers\StoresController@PostEditInventory');
     Route::Post('PostSettlement/{id}', 'App\Http\Controllers\StoresController@PostSettlement');
     Route::get('InventoryPrint/{id}', 'App\Http\Controllers\StoresController@InventoryPrint');



   //ItemsGuide
      Route::get('ItemsGuide', 'App\Http\Controllers\StoresController@ItemsGuidePage');
      Route::get('ItemsGuide2', 'App\Http\Controllers\StoresController@ItemsGuide2');
      Route::get('UpdatePrice/{id}/{P}/{PP}/{PPP}/{Code}', 'App\Http\Controllers\StoresController@UpdatePrice');
      Route::get('TypeGuideFilter', 'App\Http\Controllers\StoresController@TypeGuideFilter');
      Route::get('GroupGuideFilter', 'App\Http\Controllers\StoresController@GroupGuideFilter');
      Route::get('BrandsGuideFilter', 'App\Http\Controllers\StoresController@BrandsGuideFilter');
      Route::get('NameGuideilter', 'App\Http\Controllers\StoresController@NameGuideilter');
      Route::get('CodeGuideFilter', 'App\Http\Controllers\StoresController@CodeGuideFilter');
      Route::post('ChangeProductPrice', 'App\Http\Controllers\StoresController@ChangeProductPrice');


        //NewChangePrice
          Route::get('NewChangePrice', 'App\Http\Controllers\StoresController@NewChangePrice');
        Route::get('NewTypeGuideFilter', 'App\Http\Controllers\StoresController@NewTypeGuideFilter');
      Route::get('NewNameGuideilter', 'App\Http\Controllers\StoresController@NewNameGuideilter');
      Route::get('NewCodeGuideFilter', 'App\Http\Controllers\StoresController@NewCodeGuideFilter');
      Route::get('NewGroupGuideFilter', 'App\Http\Controllers\StoresController@NewGroupGuideFilter');
      Route::post('PostChangePrice', 'App\Http\Controllers\StoresController@PostChangePrice');






    //StoresTransfers
      Route::get('StoresTransfers', 'App\Http\Controllers\StoresController@StoresTransfersPage');
      Route::get('StoresTransfersSechdule', 'App\Http\Controllers\StoresController@StoresTransfersSechdulePage');

      Route::get('UnitNameCodeStoresTransferFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');
      Route::get('UnitNameCodeStoresTransferFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
      Route::get('UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');
      Route::get('TarnsferStoresFilter', 'App\Http\Controllers\StoresController@TarnsferStoresFilter');
      Route::Post('AddStoresTransfers', 'App\Http\Controllers\StoresController@AddStoresTransfers');
      Route::Post('SureStoresTransfers/{id}', 'App\Http\Controllers\StoresController@SureStoresTransfers');
      Route::Post('PostEditStoresTransfers/{id}', 'App\Http\Controllers\StoresController@PostEditStoresTransfers');
      Route::Post('SureRecivedShipStoresTransfers/{id}', 'App\Http\Controllers\StoresController@SureRecivedShipStoresTransfers');
      Route::get('TransferSure/{id}', 'App\Http\Controllers\StoresController@TransferSure');
      Route::get('RefusedStoreTransfer/{id}', 'App\Http\Controllers\StoresController@RefusedStoreTransfer');
      Route::get('EditStoreTransfer/{id}', 'App\Http\Controllers\StoresController@EditStoreTransfer');
      Route::get('ShippingTransferRecived/{id}', 'App\Http\Controllers\StoresController@ShippingTransferRecived');
      Route::get('StoresTransferPrint/{id}', 'App\Http\Controllers\StoresController@StoresTransferPrint');
      Route::get('DeleteStoresTransfer/{id}', 'App\Http\Controllers\StoresController@DeleteStoresTransfer');
      Route::get('StoreClientAccount', 'App\Http\Controllers\StoresController@StoreClientAccount');


        //Return Stores Transfer
         Route::get('Stores_Sales_Transfers_Sechdule', 'App\Http\Controllers\StoresController@Stores_Sales_Transfers_Sechdule');
         Route::get('ReturnStoresTransfer/{id}', 'App\Http\Controllers\StoresController@ReturnStoresTransfer');
         Route::get('ApproveReturnStoresTransfer/{id}', 'App\Http\Controllers\StoresController@ApproveReturnStoresTransfer');
         Route::post('ReturnSaleStoresTransfers', 'App\Http\Controllers\StoresController@ReturnSaleStoresTransfers');
         Route::get('ReturnStoresTransfersSechdule', 'App\Http\Controllers\StoresController@ReturnStoresTransfersSechdule');
         Route::post('PostApproveReturnSaleStoresTransfers', 'App\Http\Controllers\StoresController@PostApproveReturnSaleStoresTransfers');



    //BarcodeـPrinting and Settings
        Route::get('BarcodeـPrinting', 'App\Http\Controllers\StoresController@BarcodeـPrintingPage');
        Route::post('UpdateBarcodePrint', 'App\Http\Controllers\StoresController@UpdateBarcodePrint');
        Route::get('BarcodeFilter', 'App\Http\Controllers\StoresController@BarcodeFilter');

        //QRـPrinting
        Route::get('QRـPrinting', 'App\Http\Controllers\StoresController@QRـPrinting');
        Route::post('UpdateQRـPrinting', 'App\Http\Controllers\StoresController@UpdateQRـPrinting');


       //settings
        Route::get('BarcodeـPrinting_Settings', 'App\Http\Controllers\StoresController@BarcodeـPrinting_SettingsPage');
        Route::Post('AddBaPriSett', 'App\Http\Controllers\StoresController@AddBaPriSett');
        Route::Post('EditBaPriSett/{id}', 'App\Http\Controllers\StoresController@EditBaPriSett');
        Route::get('DeleteBaPriSett/{id}', 'App\Http\Controllers\StoresController@DeleteBaPriSett');


    //ShippingCompany
        Route::get('ShippingCompany', 'App\Http\Controllers\StoresController@ShippingCompanyPage');
        Route::post('AddShippingCompany', 'App\Http\Controllers\StoresController@AddShippingCompany');
        Route::post('EditShippingCompany/{id}', 'App\Http\Controllers\StoresController@EditShippingCompany');
        Route::get('DeleteShippingCompany/{id}', 'App\Http\Controllers\StoresController@DeleteShippingCompany');

        //Permission_to_exchange_goods
        Route::get('Permission_to_exchange_goods', 'App\Http\Controllers\StoresController@Permission_to_exchange_goods');

        Route::get('ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');
        Route::get('RecivedProductsFilter', 'App\Http\Controllers\StoresController@RecivedProductsFilter');
        Route::get('EditPremissionExchange/ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');
        Route::get('EditPremissionRercived/ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');
        Route::get('EditPremissionRercived/RecivedProductsFilter', 'App\Http\Controllers\StoresController@RecivedProductsFilter');

        Route::post('AddExchangeGoods', 'App\Http\Controllers\StoresController@AddExchangeGoods');
        Route::get('ExchangePrint/{id}', 'App\Http\Controllers\StoresController@ExchangePrintPage');
        Route::get('DeletePremissionExchange/{id}', 'App\Http\Controllers\StoresController@DeletePremissionExchange');
        Route::get('ExchangeGoodsSechdule', 'App\Http\Controllers\StoresController@ExchangeGoodsSechdulePage');
        Route::get('EditPremissionExchange/{id}', 'App\Http\Controllers\StoresController@EditPremissionExchange');
        Route::post('PostEditExchangeGoods/{id}', 'App\Http\Controllers\StoresController@PostEditExchangeGoods');
        Route::get('TransferToSalesExchange/{id}', 'App\Http\Controllers\StoresController@TransferToSalesExchange');

    //Permission_to_receive_goods
       Route::get('Permission_to_receive_goods', 'App\Http\Controllers\StoresController@Permission_to_receive_goods');
       Route::post('AddRecivedGoods', 'App\Http\Controllers\StoresController@AddRecivedGoods');
       Route::get('RecivedPrint/{id}', 'App\Http\Controllers\StoresController@RecivedPrint');
       Route::get('ReceiveGoodsSechdule', 'App\Http\Controllers\StoresController@ReceiveGoodsSechdule');
        Route::get('DeletePremissionRercived/{id}', 'App\Http\Controllers\StoresController@DeletePremissionRercived');
        Route::get('EditPremissionRercived/{id}', 'App\Http\Controllers\StoresController@EditPremissionRercived');
        Route::get('TransferToPurchasesRecived/{id}', 'App\Http\Controllers\StoresController@TransferToPurchasesRecived');
        Route::post('PostEditRecivedGoods/{id}', 'App\Http\Controllers\StoresController@PostEditRecivedGoods');

    //Consists
       Route::get('Consists', 'App\Http\Controllers\StoresController@ConsistsPage');
       Route::get('ConsistFilter', 'App\Http\Controllers\StoresController@ConsistFilter');
       Route::post('AddConsists', 'App\Http\Controllers\StoresController@AddConsists');
       Route::get('ConsistsSechdule', 'App\Http\Controllers\StoresController@ConsistsSechdule');


        //StoresQty
          Route::get('StoresQty', 'App\Http\Controllers\StoresController@StoresQtyPage');
          Route::get('StoresQtyFilter', 'App\Http\Controllers\StoresController@StoresQtyFilter');
          Route::get('ChangeStroeQty', 'App\Http\Controllers\StoresController@ChangeStroeQty');
          Route::get('DeleteStoreQty/{id}', 'App\Http\Controllers\StoresController@DeleteStoreQty');


// ===  End Stores  ========================================================================================================

// === HR ========================================================================================================


        //Work Departments
Route::get('WorkDepartments', 'App\Http\Controllers\HRController@WorkDepartmentsPage');
Route::post('AddWorkDepartments', 'App\Http\Controllers\HRController@AddWorkDepartments');
Route::post('EditWorkDepartments', 'App\Http\Controllers\HRController@EditWorkDepartments');
Route::get('DeleteWorkDepartments', 'App\Http\Controllers\HRController@DeleteWorkDepartments');
Route::get('EditDepartment', 'App\Http\Controllers\HRController@EditDepartment');

        //Employment_levels
        Route::get('Employment_levels', 'App\Http\Controllers\HRController@Employment_levels');
Route::post('AddEmployment_levels', 'App\Http\Controllers\HRController@AddEmployment_levels');
Route::post('EditEmployment_levels', 'App\Http\Controllers\HRController@EditEmployment_levels');
Route::get('DeleteEmployment_levels', 'App\Http\Controllers\HRController@DeleteEmployment_levels');


        //Insurance_companies
        Route::get('Insurance_companies', 'App\Http\Controllers\HRController@Insurance_companies');
Route::post('AddInsurance_companies', 'App\Http\Controllers\HRController@AddInsurance_companies');
Route::post('EditInsurance_companies', 'App\Http\Controllers\HRController@EditInsurance_companies');
Route::get('DeleteInsurance_companies', 'App\Http\Controllers\HRController@DeleteInsurance_companies');


            //Jobs_Type
Route::get('Jobs_Type', 'App\Http\Controllers\HRController@Jobs_TypePage');
Route::post('AddJobs_Type', 'App\Http\Controllers\HRController@AddJobs_Type');
Route::post('EditJobs_Type/{id}', 'App\Http\Controllers\HRController@EditJobs_Type');
Route::get('DeleteJobs_Type/{id}', 'App\Http\Controllers\HRController@DeleteJobs_Type');


            //Benefits_Types
Route::get('Benefits_Types', 'App\Http\Controllers\HRController@Benefits_TypesPage');
Route::post('AddBenefits_Types', 'App\Http\Controllers\HRController@AddBenefits_Types');
Route::post('EditBenefits_Types/{id}', 'App\Http\Controllers\HRController@EditBenefits_Types');
Route::get('DeleteBenefits_Types/{id}', 'App\Http\Controllers\HRController@DeleteBenefits_Types');

            //Deductions_Types
Route::get('Deductions_Types', 'App\Http\Controllers\HRController@Deductions_TypesPage');
Route::post('AddDeductions_Types', 'App\Http\Controllers\HRController@AddDeductions_Types');
Route::post('EditDeductions_Types/{id}', 'App\Http\Controllers\HRController@EditDeductions_Types');
Route::get('DeleteDeductions_Types/{id}', 'App\Http\Controllers\HRController@DeleteDeductions_Types');

            //Holidays_Types
Route::get('Holidays_Types', 'App\Http\Controllers\HRController@Holidays_TypesPage');
Route::post('AddHolidays_Types', 'App\Http\Controllers\HRController@AddHolidays_Types');
Route::post('EditHolidays_Types/{id}', 'App\Http\Controllers\HRController@EditHolidays_Types');
Route::get('DeleteHolidays_Types/{id}', 'App\Http\Controllers\HRController@DeleteHolidays_Types');

            //Overtime
Route::get('Overtime', 'App\Http\Controllers\HRController@OvertimePage');
Route::post('AddOvertime', 'App\Http\Controllers\HRController@AddOvertime');
Route::post('EditOvertime/{id}', 'App\Http\Controllers\HRController@EditOvertime');
Route::get('DeleteOvertime/{id}', 'App\Http\Controllers\HRController@DeleteOvertime');

//Employee
    Route::get('AddEmp', 'App\Http\Controllers\HRController@AddEmpPage');
    Route::get('EmpSechdule', 'App\Http\Controllers\HRController@EmpSechdulePage');
    Route::get('JobRequestsSechdule', 'App\Http\Controllers\HRController@JobRequestsSechdule');
    Route::get('EditEmp/{id}', 'App\Http\Controllers\HRController@EditEmp');
    Route::get('DeleteEmp/{id}', 'App\Http\Controllers\HRController@DeleteEmp');
    Route::post('PostAddEmp', 'App\Http\Controllers\HRController@PostAddEmp');
    Route::post('PostEditEmp/{id}', 'App\Http\Controllers\HRController@PostEditEmp');
    Route::get('TransToEmp/{id}', 'App\Http\Controllers\HRController@TransToEmp');
    Route::get('PrintEmp/{id}', 'App\Http\Controllers\HRController@PrintEmp');
    Route::get('PrintCardEmp/{id}', 'App\Http\Controllers\HRController@PrintCardEmp');
    Route::get('UnActiveEmp/{id}', 'App\Http\Controllers\HRController@UnActiveEmp');
    Route::get('ActiveEmp/{id}', 'App\Http\Controllers\HRController@ActiveEmp');


        //ProducationPoints
Route::get('ProducationPoints', 'App\Http\Controllers\HRController@ProducationPoints');
Route::post('AddProducationPoints', 'App\Http\Controllers\HRController@AddProducationPoints');
Route::post('EditProducationPoints/{id}', 'App\Http\Controllers\HRController@EditProducationPoints');
Route::get('DeleteProducationPoints/{id}', 'App\Http\Controllers\HRController@DeleteProducationPoints');

            //Loan_Types
Route::get('Loan_Types', 'App\Http\Controllers\HRController@Loan_TypesPage');
Route::post('AddLoan_Types', 'App\Http\Controllers\HRController@AddLoan_Types');
Route::post('EditLoan_Types/{id}', 'App\Http\Controllers\HRController@EditLoan_Types');
Route::get('DeleteLoan_Types/{id}', 'App\Http\Controllers\HRController@DeleteLoan_Types');


    //Borrows
         Route::get('Borrow', 'App\Http\Controllers\HRController@BorrowPage');
         Route::get('AddBorrow', 'App\Http\Controllers\HRController@AddBorrowPage');
         Route::get('EmpCheck/{Emp}/{Month}', 'App\Http\Controllers\HRController@EmpCheck');
         Route::post('PostAddBorrow', 'App\Http\Controllers\HRController@PostAddBorrow');
         Route::get('DeleteBorrow/{id}', 'App\Http\Controllers\HRController@DeleteBorrow');

                //Entitlements
Route::get('Entitlements', 'App\Http\Controllers\HRController@EntitlementsPage');
Route::post('AddEntitlements', 'App\Http\Controllers\HRController@AddEntitlements');
Route::post('EditEntitlements/{id}', 'App\Http\Controllers\HRController@EditEntitlements');
Route::get('DeleteEntitlements/{id}', 'App\Http\Controllers\HRController@DeleteEntitlements');

                //Deducation
Route::get('Deducation', 'App\Http\Controllers\HRController@DeducationPage');
Route::post('AddDeducation', 'App\Http\Controllers\HRController@AddDeducation');
Route::post('EditDeducation/{id}', 'App\Http\Controllers\HRController@EditDeducation');
Route::get('DeleteDeducation/{id}', 'App\Http\Controllers\HRController@DeleteDeducation');


    //Holidays
    Route::get('Holidays', 'App\Http\Controllers\HRController@HolidaysPage');
    Route::post('AddHolidays', 'App\Http\Controllers\HRController@AddHolidays');
    Route::post('EditHolidays/{id}', 'App\Http\Controllers\HRController@EditHolidays');
    Route::get('DeleteHolidays/{id}', 'App\Http\Controllers\HRController@DeleteHolidays');
    Route::get('HolidaysTypeFilter/{type}', 'App\Http\Controllers\HRController@HolidaysTypeFilter');


        //HolidaysOrder
    Route::get('HolidaysOrder', 'App\Http\Controllers\HRController@HolidaysOrderPage');
    Route::post('AddHolidaysOrder', 'App\Http\Controllers\HRController@AddHolidaysOrder');
    Route::post('EditHolidaysOrder/{id}', 'App\Http\Controllers\HRController@EditHolidaysOrder');
    Route::get('DeleteHolidaysOrder/{id}', 'App\Http\Controllers\HRController@DeleteHolidaysOrder');
    Route::get('TransToHoilday/{id}', 'App\Http\Controllers\HRController@TransToHoilday');
    Route::get('HolidaysOrderTypeFilter/{type}', 'App\Http\Controllers\HRController@HolidaysOrderTypeFilter');


//Attendance
    Route::get('Attendance', 'App\Http\Controllers\HRController@AttendancePage');
    Route::get('AttendanceSechdule', 'App\Http\Controllers\HRController@AttendanceSechdulePage');
    Route::get('EmpNameFilter/{Emp}', 'App\Http\Controllers\HRController@EmpNameFilter');
    Route::get('EditAttendance/EmpNameFilter/{Emp}', 'App\Http\Controllers\HRController@EmpNameFilter');
    Route::post('AddAttendance', 'App\Http\Controllers\HRController@AddAttendance');
    Route::post('PostEditAttendance/{id}', 'App\Http\Controllers\HRController@PostEditAttendance');
    Route::get('EditAttendance/{id}', 'App\Http\Controllers\HRController@EditAttendancePage');
    Route::get('DeleteAttendance/{id}', 'App\Http\Controllers\HRController@DeleteAttendance');

    //Departure
      Route::get('Departure/{id}', 'App\Http\Controllers\HRController@DeparturePage');
      Route::post('AddDeparture/{id}', 'App\Http\Controllers\HRController@AddDeparture');
      Route::get('DepartureSechdule', 'App\Http\Controllers\HRController@DepartureSechdulePage');
      Route::get('EditDeparture/{id}', 'App\Http\Controllers\HRController@EditDeparturePage');
      Route::post('PostEditDeparture/{id}', 'App\Http\Controllers\HRController@PostEditDeparture');


    //RegOverTime
     Route::get('RegOverTime', 'App\Http\Controllers\HRController@RegOverTimePage');
     Route::post('AddRegOverTime', 'App\Http\Controllers\HRController@AddRegOverTime');
     Route::post('EditRegOverTime/{id}', 'App\Http\Controllers\HRController@EditRegOverTime');
     Route::get('DeleteRegOverTime/{id}', 'App\Http\Controllers\HRController@DeleteRegOverTime');
     Route::get('OverTimeTypeFilter/{type}/{emp}', 'App\Http\Controllers\HRController@OverTimeTypeFilter');


  //Loan
     Route::get('AddLoan', 'App\Http\Controllers\HRController@AddLoanPage');
     Route::post('PostAddLoan', 'App\Http\Controllers\HRController@PostAddLoan');
     Route::get('Loan', 'App\Http\Controllers\HRController@LoanPage');
     Route::get('DeleteLoan/{id}', 'App\Http\Controllers\HRController@DeleteLoan');

    //EmpInstallment
     Route::get('EmpInstallment', 'App\Http\Controllers\HRController@EmpInstallmentPage');
       Route::get('EmpInstallBillDone/{id}', 'App\Http\Controllers\HRController@EmpInstallBillDone');
        Route::get('EmpUnInstallBill/{id}', 'App\Http\Controllers\HRController@EmpUnInstallBill');
        Route::post('EmpInstallDone', 'App\Http\Controllers\HRController@EmpInstallDone');
        Route::get('EmpUnInstall/{id}', 'App\Http\Controllers\HRController@EmpUnInstall');
        Route::get('InstallEmpPrint/{id}/{inst}', 'App\Http\Controllers\HRController@InstallEmpPrint');


//Salary
     Route::get('AddSalary', 'App\Http\Controllers\HRController@AddSalaryPage');
     Route::get('SalarySechdules', 'App\Http\Controllers\HRController@SalarySechdulesPage');
     Route::get('EmpCheckSalary/{Emp}/{Month}', 'App\Http\Controllers\HRController@EmpCheckSalary');
     Route::post('PostAddSalary', 'App\Http\Controllers\HRController@PostAddSalary');
     Route::get('DeletePaySalary/{id}', 'App\Http\Controllers\HRController@DeletePaySalary');


//ExchangeCommissions
     Route::get('ExchangeCommissions', 'App\Http\Controllers\HRController@ExchangeCommissionsPage');
    Route::get('EmpCheckCommision/{Emp}', 'App\Http\Controllers\HRController@EmpCheckCommision');
    Route::post('PostExchangeCommissions', 'App\Http\Controllers\HRController@PostExchangeCommissions');
    Route::get('ExchangeCommissionsSechdule', 'App\Http\Controllers\HRController@ExchangeCommissionsSechdule');
    Route::get('DeleteCommission/{id}', 'App\Http\Controllers\HRController@DeleteCommission');


        //MyGoals
         Route::get('MyGoals', 'App\Http\Controllers\HRController@MyGoals');

        //ResignationRequest
    Route::get('ResignationRequestSechdule', 'App\Http\Controllers\HRController@ResignationRequestSechdule');
    Route::get('ResignationRequest', 'App\Http\Controllers\HRController@ResignationRequest');
    Route::post('AddResignationRequest', 'App\Http\Controllers\HRController@AddResignationRequest');
    Route::post('EditResignationRequest/{id}', 'App\Http\Controllers\HRController@EditResignationRequest');
    Route::get('DeleteResignationRequest/{id}', 'App\Http\Controllers\HRController@DeleteResignationRequest');
    Route::get('AcceptResignation/{id}', 'App\Http\Controllers\HRController@AcceptResignation');
    Route::get('RefuseResignation', 'App\Http\Controllers\HRController@RefuseResignation');

        //Disclaimer
        Route::get('Disclaimer', 'App\Http\Controllers\HRController@Disclaimer');
           Route::post('AddDisclaimer', 'App\Http\Controllers\HRController@AddDisclaimer');
    Route::post('EditDisclaimer/{id}', 'App\Http\Controllers\HRController@EditDisclaimer');
    Route::get('DeleteDisclaimer/{id}', 'App\Http\Controllers\HRController@DeleteDisclaimer');
    Route::get('PrintDisclaimer/{id}', 'App\Http\Controllers\HRController@PrintDisclaimer');

// === End HR ===================================================================================================

// ===  HR Reports  ===================================================================================================


        //AttendenceAndDepartureReport
  Route::get('AttendenceAndDepartureReport', 'App\Http\Controllers\HRReportsController@AttendenceAndDepartureReport');
Route::get('AttendenceAndDepartureReportFilterTwo', 'App\Http\Controllers\HRReportsController@AttendenceAndDepartureReportFilterTwo');



        //AttendenceValueReport
          Route::get('AttendenceValueReport', 'App\Http\Controllers\HRReportsController@AttendenceValueReport');
Route::get('AttendenceValueReportFilterTwo', 'App\Http\Controllers\HRReportsController@AttendenceValueReportFilterTwo');


        //SalaryPayed
          Route::get('SalaryPayed', 'App\Http\Controllers\HRReportsController@SalaryPayed');
Route::get('SalaryPayedFilterTwo', 'App\Http\Controllers\HRReportsController@SalaryPayedFilterTwo');

        //PaySalaryReport
           Route::get('PaySalaryReport', 'App\Http\Controllers\HRReportsController@PaySalaryReport');
           Route::get('NewEmpCheckSalary/{Emp}/{Month}', 'App\Http\Controllers\HRReportsController@NewEmpCheckSalary');



        //EmpSalaries
           Route::get('EmpSalaries', 'App\Http\Controllers\HRReportsController@EmpSalaries');

// === End HR  Reports  ===================================================================================================





// === Purchases ===================================================================================================

    // == Vendors ==
    Route::get('Vendors', 'App\Http\Controllers\PurchasesController@VendorsPage');
    Route::post('AddVendors', 'App\Http\Controllers\PurchasesController@AddVendors');
    Route::post('EditVendors/{id}', 'App\Http\Controllers\PurchasesController@EditVendors');
    Route::get('DeleteVendors/{id}', 'App\Http\Controllers\PurchasesController@DeleteVendors');

    //  ====  Purchases Order ====
        Route::get('PurchasesOrder', 'App\Http\Controllers\PurchasesController@PurchasesOrderPage');
        Route::get('PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
        Route::get('NewPurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@NewPurchacesProductsFilter');
        Route::get('PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');
        Route::get('UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');
        Route::get('AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
        Route::get('StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');
        Route::get('TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');
        Route::get('ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');
        Route::post('AddPurchasesOrder', 'App\Http\Controllers\PurchasesController@AddPurchasesOrder');
        Route::get('PurchasesOrderSechdule', 'App\Http\Controllers\PurchasesController@PurchasesOrderSechdule');
        Route::get('EditPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@EditPurchasesOrder');
        Route::get('DeletePurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@DeletePurchasesOrder');
        Route::get('PurchOrdPrint/{id}', 'App\Http\Controllers\PurchasesController@PurchOrdPrint');
       Route::get('FilterBillPurchasesOrder', 'App\Http\Controllers\PurchasesController@FilterBillPurchasesOrder');

        Route::get('EditPurchasesOrder/{id}/PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
        Route::get('EditPurchasesOrder/{id}/PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');

        Route::get('EditPurchasesOrder/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

        Route::get('EditManufacturingModel/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

        Route::get('EditPurchasesOrder/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');


        Route::get('EditPurchasesOrder/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

        Route::get('EditPurchasesOrder/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('HoldSale/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('OpenMaintainceBill/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');

        Route::get('EditPurchasesOrder/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
        Route::get('TransferToPurchases/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
        Route::get('TransferToPurchasesRecived/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');

        Route::get('EditManufacturingModel/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

        Route::get('EditPurchasesOrder/ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');

        Route::get('EditPurchasesOrder/ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');

     Route::post('PostEditPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@PostEditPurchasesOrder');
     Route::post('PostTransferPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchases');
     Route::post('PostTransferPurchasesRecived/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchasesRecived');

      Route::get('TransferToPurchases/{id}', 'App\Http\Controllers\PurchasesController@TransferToPurchases');
      Route::post('PostQuality/{id}', 'App\Http\Controllers\PurchasesController@PostQuality');
      Route::get('RefusedQualityPurchOrder/{id}', 'App\Http\Controllers\PurchasesController@RefusedQualityPurchOrder');
      Route::get('PurchOrderQty/{id}', 'App\Http\Controllers\PurchasesController@PurchOrderQtyPage');
      Route::post('RenewalPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@RenewalPurchasesOrder');


    //==== End Purchases Order =======

    //Change Price Unit Ajax
     Route::get('ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');
     Route::get('EditPurchasesOrder/ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');

    //Purchases
       Route::get('Purchases', 'App\Http\Controllers\PurchasesController@PurchasesPage');
       Route::get('PurchasesSechdule', 'App\Http\Controllers\PurchasesController@PurchasesSechdulePage');
       Route::get('PurchasesSechduleTax', 'App\Http\Controllers\PurchasesController@PurchasesSechduleTax');
       Route::post('AddPurchases', 'App\Http\Controllers\PurchasesController@AddPurchases');
       Route::get('PurchPrint/{id}', 'App\Http\Controllers\PurchasesController@PurchPrint');
       Route::get('ReturnPurch/{id}', 'App\Http\Controllers\PurchasesController@ReturnPurchPage');
       Route::get('ReturnPurchasesSechdule', 'App\Http\Controllers\PurchasesController@ReturnPurchasesSechdulePage');
       Route::get('FilterBillPurchases', 'App\Http\Controllers\PurchasesController@FilterBillPurchases');
       Route::get('FilterBillPurchasesHold', 'App\Http\Controllers\PurchasesController@FilterBillPurchasesHold');
       Route::post('PostReturnPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostReturnPurchases');
       Route::get('Barcode/{id}', 'App\Http\Controllers\PurchasesController@BarcodePage');
       Route::get('EditPuechasesBill/{id}', 'App\Http\Controllers\PurchasesController@EditPuechasesBillPage');
       Route::get('DeletePurchaseBill/{id}', 'App\Http\Controllers\PurchasesController@DeletePurchaseBill');


       Route::get('StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');
       Route::get('EditPuechasesBill/StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');
       Route::get('EditPurchasesOrder/StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');


       Route::get('EditPuechasesBill/{id}/PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
       Route::get('EditPuechasesBill/{id}/PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');

       Route::get('EditPuechasesBill/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

       Route::get('EditPuechasesBill/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');

       Route::get('EditPuechasesBill/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

       Route::get('EditPuechasesBill/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');

       Route::get('EditPuechasesBill/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
       Route::get('EditPuechasesBill/ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');

       Route::get('EditPuechasesBill/ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');

       Route::get('EditPuechasesBill/AddNewVendor/{co}/{name}/{price}', 'App\Http\Controllers\PurchasesController@AddNewVendor');

       Route::get('EditPuechasesBill/AddNewProduct/{Name}/{EnName}/{P_Type}/{Brand}/{Group}/{unit}/{Rate}/{Barcode}/{Price}/{Price_Two}/{Price_Three}', 'App\Http\Controllers\StoresController@AddNewProduct');


       Route::get('EditPuechasesBill/EditPurchasesOrder/ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');

       Route::post('PostEditPurchasesBill', 'App\Http\Controllers\PurchasesController@PostEditPurchasesBill');

       //PurchasesHold
       Route::get('PurchasesHold', 'App\Http\Controllers\PurchasesController@PurchasesHoldPage');
       Route::get('RecivedPurch/{id}', 'App\Http\Controllers\PurchasesController@RecivedPurchPage');
       Route::post('PostRecivedPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostRecivedPurchases');

       //Recived_Products
       Route::get('Recived_Products', 'App\Http\Controllers\PurchasesController@Recived_ProductsPage');
       Route::get('DeleteRecivedProducts/{id}', 'App\Http\Controllers\PurchasesController@DeleteRecivedProducts');

    //Shortcomings
        Route::get('Shortcomings', 'App\Http\Controllers\PurchasesController@ShortcomingsPage');
        Route::get('ShortcomingsFilter', 'App\Http\Controllers\PurchasesController@ShortcomingsProductsFilter');
        Route::post('AddShortcomings', 'App\Http\Controllers\PurchasesController@AddShortcomings');
        Route::get('ShortcomingsPrint/{id}', 'App\Http\Controllers\PurchasesController@ShortcomingsPrintPage');
        Route::get('ShortcomingsSechdule', 'App\Http\Controllers\PurchasesController@ShortcomingsSechdulePage');
        Route::get('EditShortcomings', 'App\Http\Controllers\PurchasesController@EditShortcomingsPage');
        Route::get('DeleteShortcomings/{id}', 'App\Http\Controllers\PurchasesController@DeleteShortcomings');
        Route::get('TransferToPurchasesShortcomings/{id}', 'App\Http\Controllers\PurchasesController@TransferToPurchasesShortcomings');
        Route::post('PostEditShortcomings', 'App\Http\Controllers\PurchasesController@PostEditShortcomings');
        Route::post('PostTransferPurchasesShortcomings/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchasesShortcomings');

        Route::get('ManuProdsFilter', 'App\Http\Controllers\PurchasesController@ManuProdsFilter');

// === End Purchases ===================================================================================================


//==== Sales ==============================================================================================================


                    //Clients_Group
Route::get('Clients_Group', 'App\Http\Controllers\SalesController@Clients_GroupPage');
Route::post('AddClients_Group', 'App\Http\Controllers\SalesController@AddClients_Group');
Route::post('EditClients_Group/{id}', 'App\Http\Controllers\SalesController@EditClients_Group');
Route::get('DeleteClients_Group/{id}', 'App\Http\Controllers\SalesController@DeleteClients_Group');


        //ReturnWithoutBill
        Route::get('ReturnWithoutBill', 'App\Http\Controllers\SalesController@ReturnWithoutBill');
        Route::post('AddReturnWithoutBill', 'App\Http\Controllers\SalesController@AddReturnWithoutBill');



                            //InstallmentCompanies
Route::get('InstallmentCompanies', 'App\Http\Controllers\SalesController@InstallmentCompanies');
Route::post('AddInstallmentCompanies', 'App\Http\Controllers\SalesController@AddInstallmentCompanies');
Route::post('EditInstallmentCompanies/{id}', 'App\Http\Controllers\SalesController@EditInstallmentCompanies');
Route::get('DeleteInstallmentCompanies/{id}', 'App\Http\Controllers\SalesController@DeleteInstallmentCompanies');


          //Clients
                    Route::get('Clients', 'App\Http\Controllers\SalesController@ClientsPage');
                    Route::get('AddClients', 'App\Http\Controllers\SalesController@AddClientsPage');

                    Route::get('AllProClientFilter', 'App\Http\Controllers\SalesController@AllProClientFilter');
                    Route::get('AllProClientFilterJs/{id}', 'App\Http\Controllers\SalesController@AllProClientFilterJs');
                    Route::get('EditClients/AllProClientFilter', 'App\Http\Controllers\SalesController@AllProClientFilter');
                    Route::get('EditClients/AllProClientFilterJs/{id}', 'App\Http\Controllers\SalesController@AllProClientFilterJs');

                    Route::post('PostAddClients', 'App\Http\Controllers\SalesController@PostAddClients');
                    Route::get('EditClients/{id}', 'App\Http\Controllers\SalesController@EditClientsPage');
                    Route::get('ProfileCustomer/{id}', 'App\Http\Controllers\SalesController@ProfileCustomer');
                    Route::get('CoooMCustomer/{id}', 'App\Http\Controllers\SalesController@CoooMCustomer');
                    Route::post('PostEditClients/{id}', 'App\Http\Controllers\SalesController@PostEditClients');
                    Route::get('DeleteClients/{id}', 'App\Http\Controllers\SalesController@DeleteClients');
                    Route::get('GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');
                    Route::get('PlatformFilter/{id}', 'App\Http\Controllers\SalesController@PlatformFilter');
                    Route::get('EditClients/GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('EditClients/CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');
                    Route::get('EditClients/PlatformFilter/{id}', 'App\Http\Controllers\SalesController@PlatformFilter');
                    Route::get('DeleteClientFile/{id}', 'App\Http\Controllers\SalesController@DeleteClientFile');
                    Route::get('FilterClientSechdule', 'App\Http\Controllers\SalesController@FilterClientSechdule');


         Route::get('EditTeacher/GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('EditTeacher/CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');


                 Route::get('EditStudent/GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('EditStudent/CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');
    //TicketsClient
     Route::get('TicketsClient/{id}', 'App\Http\Controllers\SalesController@TicketsClient');
     Route::post('AddTicketsClient', 'App\Http\Controllers\SalesController@AddTicketsClient');
     Route::post('EditTicketsClient/{id}', 'App\Http\Controllers\SalesController@EditTicketsClient');
     Route::get('DeleteTicketsClient/{id}', 'App\Http\Controllers\SalesController@DeleteTicketsClient');
     Route::get('SolveTicketsClient/{id}', 'App\Http\Controllers\SalesController@SolveTicketsClient');
     Route::get('UnSolveTicketsClient/{id}', 'App\Http\Controllers\SalesController@UnSolveTicketsClient');

        //CommentsClient
             Route::get('CommentsClient/{id}', 'App\Http\Controllers\SalesController@CommentsClient');
     Route::post('AddCommentsClient', 'App\Http\Controllers\SalesController@AddCommentsClient');
     Route::post('EditCommentsClient/{id}', 'App\Http\Controllers\SalesController@EditCommentsClient');
     Route::get('DeleteCommentsClient/{id}', 'App\Http\Controllers\SalesController@DeleteCommentsClient');


    //Quote
       Route::get('Quote', 'App\Http\Controllers\SalesController@QuotePage');
       Route::get('SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');

       Route::get('UnitSalesRateFilter', 'App\Http\Controllers\SalesController@UnitSalesRateFilter');
       Route::get('HoldSale/UnitSalesRateFilter', 'App\Http\Controllers\SalesController@UnitSalesRateFilter');

       Route::get('UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
       Route::get('HoldSale/UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');

       Route::get('AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('TransferToSalesExchange/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
       Route::post('AddQuote', 'App\Http\Controllers\SalesController@AddQuote');
       Route::get('QuotePrint/{id}', 'App\Http\Controllers\SalesController@QuotePrintPage');
       Route::get('Quote_Sechdule', 'App\Http\Controllers\SalesController@Quote_Sechdule');
       Route::get('DeleteQuote/{id}', 'App\Http\Controllers\SalesController@DeleteQuote');
       Route::get('EditQuote/{id}', 'App\Http\Controllers\SalesController@EditQuote');
       Route::get('TransferToSalesOrder/{id}', 'App\Http\Controllers\SalesController@TransferToSalesOrder');

       Route::get('TransferToSales/{id}', 'App\Http\Controllers\SalesController@TransferToSales');

       Route::get('EditQuote/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('EditQuote/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
       Route::get('EditSalesOrder/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('EditSalesOrder/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');

       Route::get('EditQuote/UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
       Route::get('EditQuote/UnitSalesRateFilter', 'App\Http\Controllers\SalesController@UnitSalesRateFilter');

       Route::get('EditQuote/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('HoldSale/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('ReturnSales/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('EditQuote/StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
       Route::post('PostEditQuote/{id}', 'App\Http\Controllers\SalesController@PostEditQuote');
       Route::post('PostTransSalesOrder/{id}', 'App\Http\Controllers\SalesController@PostTransSalesOrder');
       Route::get('FilterBillQuote', 'App\Http\Controllers\SalesController@FilterBillQuote');

       //QuoteImages
       Route::get('QuoteImagesSechdule', 'App\Http\Controllers\SalesController@QuoteImagesSechdule');
       Route::get('QuoteImages', 'App\Http\Controllers\SalesController@QuoteImagesPage');
       Route::get('QuoteImagesProductFilter', 'App\Http\Controllers\SalesController@QuoteImagesProductFilter');
       Route::get('EditQuoteImages/{id}/QuoteImagesProductFilter', 'App\Http\Controllers\SalesController@QuoteImagesProductFilter');
       Route::post('AddQuoteImages', 'App\Http\Controllers\SalesController@AddQuoteImages');
       Route::get('QuoteImagePrint/{id}', 'App\Http\Controllers\SalesController@QuoteImagePrint');
       Route::get('DeleteQuoteImages/{id}', 'App\Http\Controllers\SalesController@DeleteQuoteImages');
       Route::get('EditQuoteImages/{id}', 'App\Http\Controllers\SalesController@EditQuoteImages');
       Route::post('PostEditQuoteImages/{id}', 'App\Http\Controllers\SalesController@PostEditQuoteImages');


    //Add New Client By Ajax
       Route::get('AddNewClientPOS/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClientPOS');


        Route::get('AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

       Route::get('EditQuote/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

       Route::get('HoldSale/AddNewClientPOS/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClientPOS');

       Route::get('EditSalesOrder/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');
       Route::get('ESBill/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

    //Sales Order
    Route::get('SalesOrderSechdule', 'App\Http\Controllers\SalesController@SalesOrderSechdule');
    Route::get('SalesOrder', 'App\Http\Controllers\SalesController@SalesOrderPage');
    Route::get('SalesOrderProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
    Route::get('AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::post('AddSalesOrder', 'App\Http\Controllers\SalesController@AddSalesOrder');
    Route::get('SalesOrderPrint/{id}', 'App\Http\Controllers\SalesController@SalesOrderPrintPage');
    Route::get('DeleteSalesOrder/{id}', 'App\Http\Controllers\SalesController@DeleteSalesOrder');
    Route::get('EditSalesOrder/{id}', 'App\Http\Controllers\SalesController@EditSalesOrder');
    Route::get('TransferToSalesSO/{id}', 'App\Http\Controllers\SalesController@TransferToSalesSO');
    Route::get('EditSalesOrder/{id}/SalesOrderProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('ESBill/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('ESBill/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');

    Route::get('EditSalesOrder/UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
    Route::get('ESBill/UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
               Route::get('ESBill/UnitSalesRateFilter', 'App\Http\Controllers\SalesController@UnitSalesRateFilter');

    Route::get('EditSalesOrder/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToSalesExchange/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('ESBill/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TicketEdit/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('EditSalesOrder/StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::get('ESBill/StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::post('PostEditSalesOrder/{id}', 'App\Http\Controllers\SalesController@PostEditSalesOrder');
    Route::get('FilterBillSalesOrder', 'App\Http\Controllers\SalesController@FilterBillSalesOrder');
    Route::get('DelegateRecivedAccept/{id}', 'App\Http\Controllers\SalesController@DelegateRecivedAccept');
    Route::get('CancelSalesOrder/{id}', 'App\Http\Controllers\SalesController@CancelSalesOrder');

//Sales
Route::post('PostTransSales/{id}', 'App\Http\Controllers\SalesController@PostTransSales');
Route::post('PostTransSalesExchange/{id}', 'App\Http\Controllers\SalesController@PostTransSalesExchange');
Route::get('SalesSechdule', 'App\Http\Controllers\SalesController@SalesSechdule');
Route::get('SalesSechduleTax', 'App\Http\Controllers\SalesController@SalesSechduleTax');
Route::get('SalesDeliverySechdule', 'App\Http\Controllers\SalesController@SalesDeliverySechdule');
Route::get('FilterSalesDelivery', 'App\Http\Controllers\SalesController@FilterSalesDelivery');
Route::get('MyRequestDelivery', 'App\Http\Controllers\SalesController@MyRequestDelivery');
Route::get('FilterSalesMyRequestDelivery', 'App\Http\Controllers\SalesController@FilterSalesMyRequestDelivery');
Route::get('DeliveryCollectionFilter', 'App\Http\Controllers\SalesController@DeliveryCollectionFilter');
Route::post('PostCollectDelivery', 'App\Http\Controllers\SalesController@PostCollectDelivery');
Route::post('ChangeDelivery', 'App\Http\Controllers\SalesController@ChangeDelivery');
Route::get('Sales', 'App\Http\Controllers\SalesController@SalesPage');
Route::get('SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
Route::get('SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
Route::get('UnitSalesFilter/{id}/{Pro}/{Client}/{code}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
Route::get('ExpDateQtySalesFilter/{id}', 'App\Http\Controllers\SalesController@ExpDateQtySalesFilter');
Route::get('AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
Route::get('StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
Route::post('AddSales', 'App\Http\Controllers\SalesController@AddSales');
Route::get('SalesPrint/{id}', 'App\Http\Controllers\SalesController@SalesPrintPage');
Route::get('RecivedDelivery/{id}', 'App\Http\Controllers\SalesController@RecivedDelivery');
Route::get('SalesPrint8/{id}', 'App\Http\Controllers\SalesController@SalesPrint8Page');
Route::get('ReturnSalesPrint/{id}', 'App\Http\Controllers\SalesController@ReturnSalesPrint');
Route::get('ReturnSalesPrint8/{id}', 'App\Http\Controllers\SalesController@ReturnSalesPrint8');
Route::get('SalesPrint5/{id}', 'App\Http\Controllers\SalesController@SalesPrint5Page');
Route::get('FilterBillSales', 'App\Http\Controllers\SalesController@FilterBillSales');
Route::get('ESBill/{id}', 'App\Http\Controllers\SalesController@ESBillPage');
Route::get('DeleteSalesBill/{id}', 'App\Http\Controllers\SalesController@DeleteSalesBill');
Route::post('CollectionLaterBill', 'App\Http\Controllers\SalesController@CollectionLaterBill');
Route::post('CollectionDeliveryBill', 'App\Http\Controllers\SalesController@CollectionDeliveryBill');
Route::post('PostEditSalesBill', 'App\Http\Controllers\SalesController@PostEditSalesBill');
Route::get('SalesTakeGoods/{id}', 'App\Http\Controllers\SalesController@SalesTakeGoods');
Route::get('ClientDelivery/{id}', 'App\Http\Controllers\SalesController@ClientDelivery');

        //
Route::get('NewCostProductFilter', 'App\Http\Controllers\SalesController@NewCostProductFilter');
   Route::get('EditQuote/{id}/NewCostProductFilter', 'App\Http\Controllers\SalesController@NewCostProductFilter');
    Route::get('EditSalesOrder/{id}/NewCostProductFilter', 'App\Http\Controllers\SalesController@NewCostProductFilter');
   Route::get('ESBill/{id}/NewCostProductFilter', 'App\Http\Controllers\SalesController@NewCostProductFilter');
           Route::get('HoldSale/NewCostProductFilter', 'App\Http\Controllers\SalesController@NewCostProductFilter');


//InstallmentSechdule
Route::get('InstallmentSechdule', 'App\Http\Controllers\SalesController@InstallmentSechdule');
Route::get('InstallBillDone/{id}', 'App\Http\Controllers\SalesController@InstallBillDone');
Route::get('UnInstallBill/{id}', 'App\Http\Controllers\SalesController@UnInstallBill');
Route::post('InstallDone', 'App\Http\Controllers\SalesController@InstallDone');
Route::get('UnInstall/{id}', 'App\Http\Controllers\SalesController@UnInstall');
Route::get('InstallClientPrint/{id}/{inst}', 'App\Http\Controllers\SalesController@InstallClientPrint');


//Sales Hold
Route::get('SalesHoldSechdule', 'App\Http\Controllers\SalesController@SalesHoldSechdule');
Route::get('RecivedSales/{id}', 'App\Http\Controllers\SalesController@RecivedSales');
Route::get('RecivedSalesProductsPrint/{id}', 'App\Http\Controllers\SalesController@RecivedSalesProductsPrint');
Route::post('PostRecivedSales/{id}', 'App\Http\Controllers\SalesController@PostRecivedSales');
Route::get('RecivedProductsSales', 'App\Http\Controllers\SalesController@RecivedProductsSales');
Route::get('FilterBillSalesHold', 'App\Http\Controllers\SalesController@FilterBillSalesHold');

//Return Sales
Route::get('ReturnSales/{id}', 'App\Http\Controllers\SalesController@ReturnSales');
Route::post('PostReturn_Sales/{id}', 'App\Http\Controllers\SalesController@PostReturn_Sales');
Route::get('ReturnSalesSechdule', 'App\Http\Controllers\SalesController@ReturnSalesSechdulePage');
Route::get('ReturnSalesSechduleTaxPage', 'App\Http\Controllers\SalesController@ReturnSalesSechduleTaxPage');

    //POS
    Route::get('POS', 'App\Http\Controllers\SalesController@POSPage');
    Route::get('POSOtherQtyFilter', 'App\Http\Controllers\SalesController@POSOtherQtyFilter');
    Route::get('HoldSale/POSOtherQtyFilter', 'App\Http\Controllers\SalesController@POSOtherQtyFilter');
    Route::post('NewShift', 'App\Http\Controllers\SalesController@NewShift');
    Route::post('CloseShift', 'App\Http\Controllers\SalesController@CloseShift');
    Route::get('SalesProductsPOSFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSFilter');
    Route::get('SalesProductsPOSSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSSearchCodeFilter');
    Route::get('SalesHoldBillsPOSFilter', 'App\Http\Controllers\SalesController@SalesHoldBillsPOSFilter');
    Route::get('HoldSale/SalesProductsPOSFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSFilter');
    Route::get('HoldSale/SalesProductsPOSSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSSearchCodeFilter');
    Route::get('HoldSale/SalesHoldBillsPOSFilter', 'App\Http\Controllers\SalesController@SalesHoldBillsPOSFilter');
    Route::get('GroupPosFilter', 'App\Http\Controllers\SalesController@GroupPosFilter');
    Route::get('HoldSale/GroupPosFilter', 'App\Http\Controllers\SalesController@GroupPosFilter');
    Route::get('BrandsPosFilter', 'App\Http\Controllers\SalesController@BrandsPosFilter');
    Route::get('HoldSale/BrandsPosFilter', 'App\Http\Controllers\SalesController@BrandsPosFilter');
    Route::post('PostPOS', 'App\Http\Controllers\SalesController@PostPOS');
    Route::post('HoldPostPOS', 'App\Http\Controllers\SalesController@HoldPostPOS');
    Route::get('HoldSale/{id}', 'App\Http\Controllers\SalesController@HoldSale');
    Route::get('AddReciptVoucherAjax', 'App\Http\Controllers\SalesController@AddReciptVoucherAjax');
    Route::get('HoldSale/{id}/AddReciptVoucherAjax', 'App\Http\Controllers\SalesController@AddReciptVoucherAjax');
    Route::get('AddPaymentVoucherAjax', 'App\Http\Controllers\SalesController@AddPaymentVoucherAjax');
    Route::get('HoldSale/{id}/AddPaymentVoucherAjax', 'App\Http\Controllers\SalesController@AddPaymentVoucherAjax');
    Route::get('DeleteHoldBill/{id}', 'App\Http\Controllers\SalesController@DeleteHoldBill');

    Route::get('UnitsOfProducts/{id}', 'App\Http\Controllers\SalesController@UnitsOfProducts');
    Route::get('HoldSale/UnitsOfProducts/{id}', 'App\Http\Controllers\SalesController@UnitsOfProducts');
    Route::get('AllProsPosFilter', 'App\Http\Controllers\SalesController@AllProsPosFilter');
    Route::get('HoldSale/{id}/AllProsPosFilter', 'App\Http\Controllers\SalesController@AllProsPosFilter');

    Route::get('HoldSale/{id}/AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
    Route::get('HoldSale/AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
    Route::get('AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
        //SalesSubscribes
              Route::get('SalesSubscribes', 'App\Http\Controllers\SalesController@SalesSubscribesPage');
              Route::get('SalesSubscribeFilter', 'App\Http\Controllers\SalesController@SalesSubscribeFilter');
    Route::get('EditSalesSubscribes/SalesSubscribeFilter', 'App\Http\Controllers\SalesController@SalesSubscribeFilter');
              Route::post('AddSalesSubscribes', 'App\Http\Controllers\SalesController@AddSalesSubscribes');
              Route::get('SalesSubscribesSechdule', 'App\Http\Controllers\SalesController@SalesSubscribesSechdule');
              Route::get('SalesSubscribesPrint/{id}', 'App\Http\Controllers\SalesController@SalesSubscribesPrint');
              Route::get('DeleteSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@DeleteSalesSubscribes');
              Route::get('EditSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@EditSalesSubscribes');

              Route::post('PostEditSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@PostEditSalesSubscribes');


//TaxOnTotalTypeFilter
          Route::get('TaxOnTotalTypeFilter', 'App\Http\Controllers\SalesController@TaxOnTotalTypeFilter');
          Route::get('EndRecivedOrderHall/TaxOnTotalTypeFilter', 'App\Http\Controllers\SalesController@TaxOnTotalTypeFilter');
          Route::get('InstallCompanyFilter', 'App\Http\Controllers\SalesController@InstallCompanyFilter');

//StorePriceList
          Route::get('StorePriceList', 'App\Http\Controllers\SalesController@StorePriceList');
          Route::post('PriceListFilter', 'App\Http\Controllers\SalesController@PriceListFilter');



//JobOrder
           Route::get('JobOrder', 'App\Http\Controllers\SalesController@JobOrder');
           Route::get('JobOrderProductsFilter', 'App\Http\Controllers\SalesController@JobOrderProductsFilter');
           Route::post('AddJobOrder', 'App\Http\Controllers\SalesController@AddJobOrder');
           Route::get('JobOrderPrint/{id}', 'App\Http\Controllers\SalesController@JobOrderPrint');
           Route::get('JobOrderPrint8/{id}', 'App\Http\Controllers\SalesController@JobOrderPrint8');
           Route::get('JobOrderPrint5/{id}', 'App\Http\Controllers\SalesController@JobOrderPrint5');
           Route::get('DeleteJobOrder/{id}', 'App\Http\Controllers\SalesController@DeleteJobOrder');
           Route::get('JobOrderPrintEmp8/{id}', 'App\Http\Controllers\SalesController@JobOrderPrintEmp8');
           Route::get('JobOrderPrintEmp/{id}', 'App\Http\Controllers\SalesController@JobOrderPrintEmp');
           Route::get('JobOrderSechdule', 'App\Http\Controllers\SalesController@JobOrderSechdule');
           Route::post('EditJobOrder', 'App\Http\Controllers\SalesController@EditJobOrder');
           Route::post('PostEditJobOrder', 'App\Http\Controllers\SalesController@PostEditJobOrder');


        //ExecuteJobOrder
           Route::post('ExecuteJobOrder', 'App\Http\Controllers\SalesController@ExecuteJobOrder');
           Route::get('ExecuteJobOrderFilter', 'App\Http\Controllers\SalesController@ExecuteJobOrderFilter');
           Route::post('PostExecuteJobOrder', 'App\Http\Controllers\SalesController@PostExecuteJobOrder');
           Route::get('DeleteExecuteJobOrder/{id}', 'App\Http\Controllers\SalesController@DeleteExecuteJobOrder');
           Route::get('ExecuteJobOrderPrint/{id}', 'App\Http\Controllers\SalesController@ExecuteJobOrderPrint');
           Route::get('ExecuteJobOrderPrint8/{id}', 'App\Http\Controllers\SalesController@ExecuteJobOrderPrint8');
           Route::get('ExecuteJobOrderSechdule', 'App\Http\Controllers\SalesController@ExecuteJobOrderSechdule');

        //ExecuteJobOrderTransfer
                   Route::post('ExecuteJobOrderTransfer', 'App\Http\Controllers\SalesController@ExecuteJobOrderTransfer');


//======== End Sales ======================================================================================================


//======== Resturant ======================================================================================================


                        //Tables
                    Route::get('Tables', 'App\Http\Controllers\ResturantController@Tables');
                    Route::post('AddTables', 'App\Http\Controllers\ResturantController@AddTables');
                    Route::post('EditTables/{id}', 'App\Http\Controllers\ResturantController@EditTables');
                    Route::get('DeleteTables/{id}', 'App\Http\Controllers\ResturantController@DeleteTables');


        //ResturantSales
            Route::get('ResturantSales', 'App\Http\Controllers\ResturantController@ResturantSales');
            Route::get('SalesProductsResturantFilter', 'App\Http\Controllers\ResturantController@SalesProductsResturantFilter');
            Route::get('SalesProductsResturantSearchCodeFilter', 'App\Http\Controllers\ResturantController@SalesProductsResturantSearchCodeFilter');
            Route::get('SalesHoldBillsResturantFilter', 'App\Http\Controllers\ResturantController@SalesHoldBillsResturantFilter');
            Route::get('GroupResturantFilter', 'App\Http\Controllers\ResturantController@GroupResturantFilter');
            Route::get('BrandsResturantFilter', 'App\Http\Controllers\ResturantController@BrandsResturantFilter');
            Route::get('AllProsResturantFilter', 'App\Http\Controllers\ResturantController@AllProsResturantFilter');
            Route::get('AdditonsProsFilter', 'App\Http\Controllers\ResturantController@AdditonsProsFilter');
            Route::get('NoteProsFilter', 'App\Http\Controllers\ResturantController@NoteProsFilter');
            Route::get('AddNewNoteAjax', 'App\Http\Controllers\ResturantController@AddNewNoteAjax');
            Route::get('AVFilter', 'App\Http\Controllers\ResturantController@AVFilter');
            Route::get('CheckQtyAVFilter', 'App\Http\Controllers\ResturantController@CheckQtyAVFilter');
            Route::get('NewAddAVFilter', 'App\Http\Controllers\ResturantController@NewAddAVFilter');
            Route::get('DeleteCheckQtyAVFilter', 'App\Http\Controllers\ResturantController@DeleteCheckQtyAVFilter');
            Route::post('PostResturantOrder', 'App\Http\Controllers\ResturantController@PostResturantOrder');

        //Tables AJax
            Route::get('AllTables', 'App\Http\Controllers\ResturantController@AllTables');
            Route::get('AllTablesJ/{id}', 'App\Http\Controllers\ResturantController@AllTablesJ');
            Route::get('AddNewTableAjax', 'App\Http\Controllers\ResturantController@AddNewTableAjax');
            Route::get('AllReservedTables', 'App\Http\Controllers\ResturantController@AllReservedTables');
            Route::get('MergeDataTables', 'App\Http\Controllers\ResturantController@MergeDataTables');
            Route::get('SeparatingDataTables', 'App\Http\Controllers\ResturantController@SeparatingDataTables');
            Route::get('MovingDataTables', 'App\Http\Controllers\ResturantController@MovingDataTables');
            Route::get('PostMerge', 'App\Http\Controllers\ResturantController@PostMerge');
            Route::get('PostMoving', 'App\Http\Controllers\ResturantController@PostMoving');
            Route::get('PostSeparating', 'App\Http\Controllers\ResturantController@PostSeparating');


        //HoldSaleHall
          Route::get('HoldSaleHall', 'App\Http\Controllers\ResturantController@HoldSaleHall');
          Route::post('PostResturantHoldHall', 'App\Http\Controllers\ResturantController@PostResturantHoldHall');
          Route::get('CheckEmailPasstoDelete', 'App\Http\Controllers\ResturantController@CheckEmailPasstoDelete');


        //HoldSaleResturant
          Route::get('HoldSaleResturant', 'App\Http\Controllers\ResturantController@HoldSaleResturant');
          Route::post('PostResturantHold', 'App\Http\Controllers\ResturantController@PostResturantHold');





        //KitchenScreen
          Route::get('KitchenScreen', 'App\Http\Controllers\ResturantController@KitchenScreen');
          Route::get('EndKitchenOrder/{id}', 'App\Http\Controllers\ResturantController@EndKitchenOrder');
          Route::get('PrintKitchen/{id}', 'App\Http\Controllers\ResturantController@PrintKitchen');
        Route::get('EndKitchenOrderHall/{id}', 'App\Http\Controllers\ResturantController@EndKitchenOrderHall');
          Route::get('PrintKitchenHall/{id}', 'App\Http\Controllers\ResturantController@PrintKitchenHall');
        Route::get('EndKitchenOrderHallMore/{id}', 'App\Http\Controllers\ResturantController@EndKitchenOrderHallMore');
          Route::get('PrintKitchenHallMore/{id}', 'App\Http\Controllers\ResturantController@PrintKitchenHallMore');
        Route::get('EndKitchenOrderHallRemove/{id}', 'App\Http\Controllers\ResturantController@EndKitchenOrderHallRemove');
          Route::get('PrintKitchenHallRemove/{id}', 'App\Http\Controllers\ResturantController@PrintKitchenHallRemove');

          //RecivedScreen
          Route::get('RecivedScreen', 'App\Http\Controllers\ResturantController@RecivedScreen');
          Route::get('EndRecivedOrder/{id}', 'App\Http\Controllers\ResturantController@EndRecivedOrder');
          Route::get('PrintRecived/{id}', 'App\Http\Controllers\ResturantController@PrintRecived');
          Route::get('EndRecivedOrderHall/{id}', 'App\Http\Controllers\ResturantController@EndRecivedOrderHall');
          Route::get('PrintRecivedHall/{id}', 'App\Http\Controllers\ResturantController@PrintRecivedHall');

          Route::post('PostEndRecivedOrderHall', 'App\Http\Controllers\ResturantController@PostEndRecivedOrderHall');
          Route::get('SalesPrintHall/{id}', 'App\Http\Controllers\ResturantController@SalesPrintHall');
          Route::get('SalesPrintHall8/{id}', 'App\Http\Controllers\ResturantController@SalesPrintHall8');




            //Resturant WebSlider
                    Route::get('RWebSlider', 'App\Http\Controllers\ResturantController@RWebSlider');
                    Route::post('AddRWebSlider', 'App\Http\Controllers\ResturantController@AddRWebSlider');
                    Route::post('EditRWebSlider/{id}', 'App\Http\Controllers\ResturantController@EditRWebSlider');
                    Route::get('DeleteRWebSlider/{id}', 'App\Http\Controllers\ResturantController@DeleteRWebSlider');
                    Route::get('UnActiveRSlider/{id}', 'App\Http\Controllers\ResturantController@UnActiveRSlider');
                    Route::get('ActiveRSlider/{id}', 'App\Http\Controllers\ResturantController@ActiveRSlider');



       //Resturant SocialMedia
            Route::get('RSocialMedia', 'App\Http\Controllers\ResturantController@RSocialMedia');
            Route::post('RSocialMediaUpdate/{id}', 'App\Http\Controllers\ResturantController@RSocialMediaUpdate');

     //Resturant Articles
                    Route::get('RArticles', 'App\Http\Controllers\ResturantController@RArticles');
                    Route::post('AddRArticles', 'App\Http\Controllers\ResturantController@AddRArticles');
                    Route::post('EditRArticles/{id}', 'App\Http\Controllers\ResturantController@EditRArticles');
                    Route::get('DeleteRArticles/{id}', 'App\Http\Controllers\ResturantController@DeleteRArticles');

         //Resturant Polices
                    Route::get('RPolices', 'App\Http\Controllers\ResturantController@RPolices');
                    Route::post('UpdateRPolices/{id}', 'App\Http\Controllers\ResturantController@UpdateRPolices');


               //Resturant Terms
                    Route::get('RTerms', 'App\Http\Controllers\ResturantController@RTerms');
                    Route::post('UpdateRTerms/{id}', 'App\Http\Controllers\ResturantController@UpdateRTerms');



                        //Resturant CouponCode
    Route::get('RCouponCode', 'App\Http\Controllers\ResturantController@RCouponCode');
    Route::post('AddRCouponCode', 'App\Http\Controllers\ResturantController@AddRCouponCode');
    Route::post('EditRCouponCode/{id}', 'App\Http\Controllers\ResturantController@EditRCouponCode');
    Route::get('DeleteRCouponCode/{id}', 'App\Http\Controllers\ResturantController@DeleteRCouponCode');



                            //Resturant Gallery
    Route::get('RGallery', 'App\Http\Controllers\ResturantController@RGallery');
    Route::post('AddRGallery', 'App\Http\Controllers\ResturantController@AddRGallery');
    Route::post('EditRGallery/{id}', 'App\Http\Controllers\ResturantController@EditRGallery');
    Route::get('DeleteRGallery/{id}', 'App\Http\Controllers\ResturantController@DeleteRGallery');



                           //Resturant Reviews
    Route::get('RReviews', 'App\Http\Controllers\ResturantController@RReviews');
    Route::post('AddRReviews', 'App\Http\Controllers\ResturantController@AddRReviews');
    Route::post('EditRReviews/{id}', 'App\Http\Controllers\ResturantController@EditRReviews');
    Route::get('DeleteRReviews/{id}', 'App\Http\Controllers\ResturantController@DeleteRReviews');


        //Reservations
            Route::get('RReservations', 'App\Http\Controllers\ResturantController@RReservations');
    Route::get('DeleteRReservations/{id}', 'App\Http\Controllers\ResturantController@DeleteRReservations');
    Route::get('UnApproveResrvations/{id}', 'App\Http\Controllers\ResturantController@UnApproveResrvations');
    Route::get('ApproveResrvations/{id}', 'App\Http\Controllers\ResturantController@ApproveResrvations');



        //ResturantStyle
                  Route::get('ResturantStyle', 'App\Http\Controllers\ResturantController@ResturantStyle');
                    Route::post('UpdateResturantStyle/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyle');
                    Route::post('UpdateResturantStyleIndexI/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleIndexI');
                    Route::post('UpdateResturantStyleIndexII/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleIndexII');
                    Route::post('UpdateResturantStyleNavbar/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleNavbar');
                    Route::post('UpdateResturantStyleSupPage/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleSupPage');
                    Route::post('UpdateResturantStyleCart/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleCart');
                    Route::post('UpdateResturantStyleMenu/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantStyleMenu');

        //ResturantHome
                  Route::get('ResturantHome', 'App\Http\Controllers\ResturantController@ResturantHome');
                    Route::post('UpdateResturantHome/{id}', 'App\Http\Controllers\ResturantController@UpdateResturantHome');

        //RAbout
                  Route::get('RAbout', 'App\Http\Controllers\ResturantController@RAbout');
                    Route::post('UpdateRAbout/{id}', 'App\Http\Controllers\ResturantController@UpdateRAbout');

        //VideoSection
                  Route::get('VideoSection', 'App\Http\Controllers\ResturantController@VideoSection');
                    Route::post('UpdateVideoSection/{id}', 'App\Http\Controllers\ResturantController@UpdateVideoSection');


        //ResturantOrders
          Route::get('ResturantOrders', 'App\Http\Controllers\ResturantController@ResturantOrders');
          Route::get('EditSalesOrderResturant', 'App\Http\Controllers\ResturantController@EditSalesOrderResturant');


//======== End Resturant ======================================================================================================

//========== CRM  =========================================================================================================

                //Governrate
                    Route::get('Governrate', 'App\Http\Controllers\CRMController@GovernratePage');
                    Route::post('AddGovernrate', 'App\Http\Controllers\CRMController@AddGovernrate');
                    Route::post('EditGovernrate/{id}', 'App\Http\Controllers\CRMController@EditGovernrate');
                    Route::get('DeleteGovernrate/{id}', 'App\Http\Controllers\CRMController@DeleteGovernrate');

                //City
                    Route::get('City/{id}', 'App\Http\Controllers\CRMController@CityPage');
                    Route::post('AddCity', 'App\Http\Controllers\CRMController@AddCity');
                    Route::post('EditCity/{id}', 'App\Http\Controllers\CRMController@EditCity');
                    Route::get('DeleteCity/{id}', 'App\Http\Controllers\CRMController@DeleteCity');

        //Places
                 Route::get('Places/{id}', 'App\Http\Controllers\CRMController@PlacesPage');
                    Route::post('AddPlaces', 'App\Http\Controllers\CRMController@AddPlaces');
                    Route::post('EditPlaces/{id}', 'App\Http\Controllers\CRMController@EditPlaces');
                    Route::get('DeletePlaces/{id}', 'App\Http\Controllers\CRMController@DeletePlaces');


                    //Activites
                    Route::get('Activites', 'App\Http\Controllers\CRMController@ActivitesPage');
                    Route::post('AddActivites', 'App\Http\Controllers\CRMController@AddActivites');
                    Route::post('EditActivites/{id}', 'App\Http\Controllers\CRMController@EditActivites');
                    Route::get('DeleteActivites/{id}', 'App\Http\Controllers\CRMController@DeleteActivites');


                    //Clients_Status
                    Route::get('Clients_Status', 'App\Http\Controllers\CRMController@Clients_StatusPage');
                    Route::post('AddClients_Status', 'App\Http\Controllers\CRMController@AddClients_Status');
                    Route::post('EditClients_Status/{id}', 'App\Http\Controllers\CRMController@EditClients_Status');
                    Route::get('DeleteClients_Status/{id}', 'App\Http\Controllers\CRMController@DeleteClients_Status');

                 //Platforms
                    Route::get('Platforms', 'App\Http\Controllers\CRMController@PlatformsPage');
                    Route::post('AddPlatforms', 'App\Http\Controllers\CRMController@AddPlatforms');
                    Route::post('EditPlatforms/{id}', 'App\Http\Controllers\CRMController@EditPlatforms');
                    Route::get('DeletePlatforms/{id}', 'App\Http\Controllers\CRMController@DeletePlatforms');

               //Campaigns
                    Route::get('Campaigns/{id}', 'App\Http\Controllers\CRMController@CampaignsPage');
                    Route::post('AddCampaigns', 'App\Http\Controllers\CRMController@AddCampaigns');
                    Route::post('EditCampaigns/{id}', 'App\Http\Controllers\CRMController@EditCampaigns');
                    Route::get('DeleteCampaigns/{id}', 'App\Http\Controllers\CRMController@DeleteCampaigns');

         //Interviews_Types
                    Route::get('Interviews_Types', 'App\Http\Controllers\CRMController@Interviews_TypesPage');
                    Route::post('AddInterviews_Types', 'App\Http\Controllers\CRMController@AddInterviews_Types');
                    Route::post('EditInterviews_Types/{id}', 'App\Http\Controllers\CRMController@EditInterviews_Types');
                    Route::get('DeleteInterviews_Types/{id}', 'App\Http\Controllers\CRMController@DeleteInterviews_Types');

            //Interviews
                    Route::get('Interviews', 'App\Http\Controllers\CRMController@InterviewsPage');
                    Route::post('AddInterviews', 'App\Http\Controllers\CRMController@AddInterviews');
                    Route::post('EditInterviews/{id}', 'App\Http\Controllers\CRMController@EditInterviews');
                    Route::get('DeleteInterviews/{id}', 'App\Http\Controllers\CRMController@DeleteInterviews');
                    Route::get('NotDone/{id}', 'App\Http\Controllers\CRMController@NotDone');
                    Route::get('Done/{id}', 'App\Http\Controllers\CRMController@Done');
                    Route::get('AllClients', 'App\Http\Controllers\CRMController@AllClients');
                    Route::get('Rate/{id}', 'App\Http\Controllers\CRMController@Rate');
                    Route::get('FilterInterviews', 'App\Http\Controllers\CRMController@FilterInterviews');
                    Route::get('AddNewClientAjax', 'App\Http\Controllers\CRMController@AddNewClientAjax');


        //Projects
         Route::get('Projects', 'App\Http\Controllers\CRMController@ProjectsPage');
         Route::post('AddProjects', 'App\Http\Controllers\CRMController@AddProjects');
         Route::post('EditProjects/{id}', 'App\Http\Controllers\CRMController@EditProjects');
         Route::get('DeleteProjects/{id}', 'App\Http\Controllers\CRMController@DeleteProjects');
         Route::get('EndProject/{id}', 'App\Http\Controllers\CRMController@EndProject');

          //Missions
         Route::get('MyMissions', 'App\Http\Controllers\CRMController@MyMissions');
         Route::get('Missions', 'App\Http\Controllers\CRMController@MissionsPage');
         Route::post('AddMissions', 'App\Http\Controllers\CRMController@AddMissions');
         Route::post('EditMissions/{id}', 'App\Http\Controllers\CRMController@EditMissions');
         Route::get('DeleteMissions/{id}', 'App\Http\Controllers\CRMController@DeleteMissions');
         Route::get('SureMission/{id}', 'App\Http\Controllers\CRMController@SureMission');
         Route::get('SureMissionByEmp/{id}', 'App\Http\Controllers\CRMController@SureMissionByEmp');
         Route::post('AcceptMission', 'App\Http\Controllers\CRMController@AcceptMission');

        //PerivousMettings
           Route::get('PerivousMettings', 'App\Http\Controllers\CRMController@PerivousMettings');
           Route::post('AddNEWInterviews', 'App\Http\Controllers\CRMController@AddNEWInterviews');


        //Competitors
          Route::get('Competitors', 'App\Http\Controllers\CRMController@Competitors');
         Route::post('AddCompetitors', 'App\Http\Controllers\CRMController@AddCompetitors');
         Route::post('EditCompetitors/{id}', 'App\Http\Controllers\CRMController@EditCompetitors');
         Route::get('DeleteCompetitors/{id}', 'App\Http\Controllers\CRMController@DeleteCompetitors');

    //MyMettings
       Route::get('MyMettings', 'App\Http\Controllers\CRMController@MyMettingsPage');

//Customerـfollowـup
              Route::get('Customerـfollowـup', 'App\Http\Controllers\CRMController@Customerـfollowـup');
              Route::get('FilterCustomerـfollowـup', 'App\Http\Controllers\CRMController@FilterCustomerـfollowـup');
         Route::post('AddCustomerـfollowـup', 'App\Http\Controllers\CRMController@AddCustomerـfollowـup');
         Route::post('EditCustomerـfollowـup/{id}', 'App\Http\Controllers\CRMController@EditCustomerـfollowـup');
         Route::get('DeleteCustomerـfollowـup/{id}', 'App\Http\Controllers\CRMController@DeleteCustomerـfollowـup');

//=====  End CRM ==========================================================================================================


//=============== Reports =================================================================================================

    //Product Informations
        Route::get('Product_Info', 'App\Http\Controllers\ReportController@Product_InfoPage');
        Route::get('ProductInfoFilter', 'App\Http\Controllers\ReportController@ProductInfoFilter');


    //ProductOrderLimit
    Route::get('ProductOrderLimit', 'App\Http\Controllers\ReportController@ProductOrderLimitPage');
    Route::get('ProductOrderLimitFilter', 'App\Http\Controllers\ReportController@ProductOrderLimitFilter');
    Route::get('ProductOrderLimitFilterTwo', 'App\Http\Controllers\ReportController@ProductOrderLimitFilterTwo');

     //Report Start Period
    Route::get('ReportStartPeriod', 'App\Http\Controllers\ReportController@ReportStartPeriodPage');
    Route::get('ReportProductStartFilter', 'App\Http\Controllers\ReportController@ReportProductStartFilter');
    Route::get('ReportStartPeriodProductsFilterTwo', 'App\Http\Controllers\ReportController@ReportStartPeriodProductsFilterTwo');


    //SettlementsReports
       Route::get('SettlementsReports', 'App\Http\Controllers\ReportController@SettlementsReportsPage');
       Route::get('ReportProductSettlementFilter', 'App\Http\Controllers\ReportController@ReportProductSettlementFilter');
       Route::get('SettlementsReportsFilterTwo', 'App\Http\Controllers\ReportController@SettlementsReportsFilterTwo');

    //StoresCost
    Route::get('StoresCost', 'App\Http\Controllers\ReportController@StoresCostPage');
    Route::get('StoresCostFilter', 'App\Http\Controllers\ReportController@StoresCostFilter');
    Route::get('StoresCostFilterTwo', 'App\Http\Controllers\ReportController@StoresCostFilterTwo');

            //CreditStores
    Route::get('CreditStores', 'App\Http\Controllers\ReportController@CreditStores');
    Route::get('CreditStoresFilterTwo', 'App\Http\Controllers\ReportController@CreditStoresFilterTwo');



        //StoresInventory
    Route::get('StoresInventory', 'App\Http\Controllers\ReportController@StoresInventoryPage');
    Route::get('StoresInventoryFilter', 'App\Http\Controllers\ReportController@StoresInventoryFilter');
    Route::get('StoresInventoryFilterTwo', 'App\Http\Controllers\ReportController@StoresInventoryFilterTwo');


    //StagnantItems
       Route::get('StagnantItems', 'App\Http\Controllers\ReportController@StagnantItemsPage');
       Route::get('StegnantProductsFilter', 'App\Http\Controllers\ReportController@StegnantProductsFilter');
       Route::get('StagnantItemsFilterTwo', 'App\Http\Controllers\ReportController@StagnantItemsFilterTwo');


    //ItemsMoves
       Route::get('ItemsMoves', 'App\Http\Controllers\ReportController@ItemsMovesPage');
       Route::get('ProductsMovesFilter', 'App\Http\Controllers\ReportController@ProductsMovesFilter');
       Route::get('ItemsMovesFilterTwo', 'App\Http\Controllers\ReportController@ItemsMovesFilterTwo');

    //TotalExpensesSafes
         Route::get('TotalExpensesSafes', 'App\Http\Controllers\ReportController@TotalExpensesSafes');
       Route::get('TotalExpensesSafesFilter', 'App\Http\Controllers\ReportController@TotalExpensesSafesFilter');


    //StoresBalances
       Route::get('StoresBalances', 'App\Http\Controllers\ReportController@StoresBalancesPage');
       Route::get('StoresBalanceFilter', 'App\Http\Controllers\ReportController@StoresBalanceFilter');
       Route::get('StoresBalancesFilterTwo', 'App\Http\Controllers\ReportController@StoresBalancesFilterTwo');


    //NetPurchases
       Route::get('NetPurchases', 'App\Http\Controllers\ReportController@NetPurchasesPage');
       Route::get('NetPurchasesFilter', 'App\Http\Controllers\ReportController@NetPurchasesFilter');
       Route::get('NetPurchasesFilterTwo', 'App\Http\Controllers\ReportController@NetPurchasesFilterTwo');

    //NetSales
      Route::get('NetSales', 'App\Http\Controllers\ReportController@NetSalesPage');
      Route::get('NetSalesFilter', 'App\Http\Controllers\ReportController@NetSalesFilter');
      Route::get('NetSalesFilterTwo', 'App\Http\Controllers\ReportController@NetSalesFilterTwo');

    //TotalNetPurchases
      Route::get('TotalNetPurchases', 'App\Http\Controllers\ReportController@TotalNetPurchasesPage');
      Route::get('TotalNetPurchasesFilter', 'App\Http\Controllers\ReportController@TotalNetPurchasesFilter');
      Route::get('TotalNetPurchasesFilterTwo', 'App\Http\Controllers\ReportController@TotalNetPurchasesFilterTwo');

    //TotalNetSales
     Route::get('TotalNetSales', 'App\Http\Controllers\ReportController@TotalNetSalesPage');
      Route::get('TotalNetSalesFilter', 'App\Http\Controllers\ReportController@TotalNetSalesFilter');
      Route::get('TotalNetSalesFilterTwo', 'App\Http\Controllers\ReportController@TotalNetSalesFilterTwo');

    //Profits
       Route::get('Profits', 'App\Http\Controllers\ReportController@ProfitsPage');
       Route::get('ProfitsFilterrr', 'App\Http\Controllers\ReportController@ProfitsFilter');
       Route::get('ProfitsFilterTwo', 'App\Http\Controllers\ReportController@ProfitsFilterTwo');


            //ProductProfits
       Route::get('ProductProfits', 'App\Http\Controllers\ReportController@ProductProfits');
       Route::get('ProductProfitsFilter', 'App\Http\Controllers\ReportController@ProductProfitsFilter');
     Route::get('ProductsFilterTwo', 'App\Http\Controllers\ReportController@ProductsFilterTwo');

        //ProductProfitsNew
            Route::get('ProductProfitsNew', 'App\Http\Controllers\ReportController@ProductProfitsNew');
       Route::get('ProductProfitsNewFilter', 'App\Http\Controllers\ReportController@ProductProfitsNewFilter');
               Route::get('ExceptProductProfitsNewFilterTwo', 'App\Http\Controllers\ReportController@ExceptProductProfitsNewFilterTwo');



        //ShiftsReport
       Route::get('ShiftsReport', 'App\Http\Controllers\ReportController@ShiftsReportPage');
       Route::get('ShiftReportFilter', 'App\Http\Controllers\ReportController@ShiftReportFilter');

        //ShiftsDetailsReport
       Route::get('ShiftsDetailsReport', 'App\Http\Controllers\ReportController@ShiftsDetailsReportPage');
       Route::get('FilterShiftDetails', 'App\Http\Controllers\ReportController@FilterShiftDetails');

        //DailyClosing
       Route::get('DailyClosing', 'App\Http\Controllers\ReportController@DailyClosingPage');
       Route::get('FilterDailyClosing', 'App\Http\Controllers\ReportController@FilterDailyClosing');
       Route::get('FilterSalesDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterSalesDetailsDailyClosing');
       Route::get('FilterRSalesDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterRSalesDetailsDailyClosing');
       Route::get('FilterReciptDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterReciptDetailsDailyClosing');
       Route::get('FilterTransferTotDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterTransferTotDetailsDailyClosing');
       Route::get('FilterPaymenttDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterPaymenttDetailsDailyClosing');
       Route::get('FilterPurchasesDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterPurchasesDetailsDailyClosing');
       Route::get('FilterRPurchasesDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterRPurchasesDetailsDailyClosing');
       Route::get('FilterTransferFromDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterTransferFromDetailsDailyClosing');
       Route::get('FilterImportChecksDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterImportChecksDetailsDailyClosing');
       Route::get('FilterSettlement3agzDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterSettlement3agzDetailsDailyClosing');
       Route::get('FilterWaslDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterWaslDetailsDailyClosing');
       Route::get('FilterPetrolDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterPetrolDetailsDailyClosing');
       Route::get('FilterShippingDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterShippingDetailsDailyClosing');
       Route::get('FilterMaintainceDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterMaintainceDetailsDailyClosing');

       Route::get('FilterExportChecksDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterExportChecksDetailsDailyClosing');
       Route::get('FilterSettlementZyadaDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterSettlementZyadaDetailsDailyClosing');
       Route::get('FilterRMaintainceDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterRMaintainceDetailsDailyClosing');
       Route::get('FilterSalaryDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterSalaryDetailsDailyClosing');
       Route::get('FilterBorrowDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterBorrowDetailsDailyClosing');
       Route::get('FilterLoanDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterLoanDetailsDailyClosing');
       Route::get('FilterExchangeDetailsDailyClosing', 'App\Http\Controllers\ReportController@FilterExchangeDetailsDailyClosing');

       Route::get('FilterDailyClosingForm', 'App\Http\Controllers\ReportController@FilterDailyClosingForm');

        //ProductsReports
            Route::get('ProductsReports', 'App\Http\Controllers\ReportController@ProductsReportsPage');
            Route::get('ProductsReportFilter', 'App\Http\Controllers\ReportController@ProductsReportFilter');
      Route::get('ExpensesReportFilterTwo', 'App\Http\Controllers\ReportController@ExpensesReportFilterTwo');
       Route::get('EmployeeCommissionDiscountsFilterTwo', 'App\Http\Controllers\ReportController@EmployeeCommissionDiscountsFilterTwo');
       Route::get('ExceptProfitsFilterTwo', 'App\Http\Controllers\ReportController@ExceptProfitsFilterTwo');
       Route::get('ExceptProductProfitsFilterTwo', 'App\Http\Controllers\ReportController@ExceptProductProfitsFilterTwo');
//DailyShifts
          Route::get('DailyShifts', 'App\Http\Controllers\ReportController@DailyShiftsPage');
          Route::get('DailyShiftsFilter', 'App\Http\Controllers\ReportController@DailyShiftsFilter');

          //ExpensesReport
          Route::get('ExpensesReport', 'App\Http\Controllers\ReportController@ExpensesReportPage');
          Route::get('FilterExpensesReport', 'App\Http\Controllers\ReportController@FilterExpensesReport');

          //DailyProducts
          Route::get('DailyProducts', 'App\Http\Controllers\ReportController@DailyProductsPage');
          Route::get('DailyProductsFilter', 'App\Http\Controllers\ReportController@DailyProductsFilter');

          //EmployeeCommissionDiscounts
          Route::get('EmployeeCommissionDiscounts', 'App\Http\Controllers\ReportController@EmployeeCommissionDiscountsPage');
          Route::get('EmpCommisionFilter', 'App\Http\Controllers\ReportController@EmpCommisionFilter');

          //VendorPricesReport
          Route::get('VendorPricesReport', 'App\Http\Controllers\ReportController@VendorPricesReportPage');
          Route::get('VendorPricesReportFilter', 'App\Http\Controllers\ReportController@VendorPricesReportFilter');

          //GroupsSales
          Route::get('GroupsSales', 'App\Http\Controllers\ReportController@GroupsSalesPage');
          Route::get('GroupsSalesFilter', 'App\Http\Controllers\ReportController@GroupsSalesFilter');

        //BrandsSales
           Route::get('BrandsSales', 'App\Http\Controllers\ReportController@BrandsSales');
          Route::get('BrandsSalesFilter', 'App\Http\Controllers\ReportController@BrandsSalesFilter');

          //Collection_Delegates
          Route::get('Collection_Delegates', 'App\Http\Controllers\ReportController@Collection_DelegatesPage');
          Route::get('FilterCollection_Delegates', 'App\Http\Controllers\ReportController@FilterCollection_Delegates');

          //Sales_Delegates
          Route::get('Sales_Delegates', 'App\Http\Controllers\ReportController@Sales_DelegatesPage');
          Route::get('FilterSales_Delegates', 'App\Http\Controllers\ReportController@FilterSales_Delegates');
          Route::get('Sales_DelegatesFilterTwo', 'App\Http\Controllers\ReportController@Sales_DelegatesFilterTwo');

        //ClientSales
                 Route::get('ClientSales', 'App\Http\Controllers\ReportController@ClientSalesPage');
                 Route::get('ClientSalesFilter', 'App\Http\Controllers\ReportController@ClientSalesFilter');
                 Route::get('ClientSalesFilterTwo', 'App\Http\Controllers\ReportController@ClientSalesFilterTwo');

          //DelegateSalesDetails
                 Route::get('DelegateSalesDetails', 'App\Http\Controllers\ReportController@DelegateSalesDetails');
                 Route::get('DelegateSalesDetailsFilter', 'App\Http\Controllers\ReportController@DelegateSalesDetailsFilter');


          //StoresSalesDetails
                 Route::get('StoresSalesDetails', 'App\Http\Controllers\ReportController@StoresSalesDetails');
                 Route::get('StoresSalesDetailsFilter', 'App\Http\Controllers\ReportController@StoresSalesDetailsFilter');


                 //DailyMoves
                 Route::get('DailyMoves', 'App\Http\Controllers\ReportController@DailyMovesPage');
                 Route::get('DailyMovesFilter', 'App\Http\Controllers\ReportController@DailyMovesFilter');

                 //VendorPurchases
                 Route::get('VendorPurchases', 'App\Http\Controllers\ReportController@VendorPurchasesPage');
                 Route::get('VendorPurchasesFilter', 'App\Http\Controllers\ReportController@VendorPurchasesFilter');

                 //ExecutorSales
                 Route::get('ExecutorSales', 'App\Http\Controllers\ReportController@ExecutorSales');
                 Route::get('ExecutorSalesFilter', 'App\Http\Controllers\ReportController@ExecutorSalesFilter');

                 //InstallmentReport
                 Route::get('InstallmentReport', 'App\Http\Controllers\ReportController@InstallmentReport');
                 Route::get('InstallmentReportFilter', 'App\Http\Controllers\ReportController@InstallmentReportFilter');
                 Route::get('InstallmentReportFilterTwo', 'App\Http\Controllers\ReportController@InstallmentReportFilterTwo');



                 //ExceptProfits
                 Route::get('ExceptProfits', 'App\Http\Controllers\ReportController@ExceptProfits');
                 Route::get('ExceptProfitsFilter', 'App\Http\Controllers\ReportController@ExceptProfitsFilter');

                 //ExpiredProucts
                 Route::get('ExpiredProucts', 'App\Http\Controllers\ReportController@ExpiredProucts');
                 Route::get('ExpiredProuctsFilter', 'App\Http\Controllers\ReportController@ExpiredProuctsFilter');
                 Route::get('ExpiredProuctsFilterTwo', 'App\Http\Controllers\ReportController@ExpiredProuctsFilterTwo');

        //TotalDailyMoves
               Route::get('TotalDailyMoves', 'App\Http\Controllers\ReportController@TotalDailyMoves');
                 Route::get('TotalDailyMovesFilter', 'App\Http\Controllers\ReportController@TotalDailyMovesFilter');

        //ProfitGroupsReport
             Route::get('ProfitGroupsReport', 'App\Http\Controllers\ReportController@ProfitGroupsReport');
                 Route::get('ProfitGroupsReportFilter', 'App\Http\Controllers\ReportController@ProfitGroupsReportFilter');

        //New Reports =========================================

           //SalesBills
            Route::get('SalesBills', 'App\Http\Controllers\NewReportController@SalesBills');
            Route::post('SalesBillFilter', 'App\Http\Controllers\NewReportController@SalesBillFilter');
            Route::get('SalesBillFilterNeww', 'App\Http\Controllers\NewReportController@SalesBillFilterNeww');
            Route::get('SaveDefaultColumnsSalesBills', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsSalesBills');
            Route::get('SalesBillsReportPrint/{type}', 'App\Http\Controllers\NewReportController@SalesBillsReportPrint');

        //MaintanceSalesReport
            Route::get('MaintanceSalesReport', 'App\Http\Controllers\NewReportController@MaintanceSalesReport');
            Route::get('MaintanceSalesReportFilterTwo', 'App\Http\Controllers\NewReportController@MaintanceSalesReportFilterTwo');

        //Maintenance_Tune
             Route::get('Maintenance_Tune', 'App\Http\Controllers\NewReportController@Maintenance_Tune');
            Route::get('Maintenance_TuneFilterTwo', 'App\Http\Controllers\NewReportController@Maintenance_TuneFilterTwo');


        //PurchasesBills
            Route::get('PurchasesBills', 'App\Http\Controllers\NewReportController@PurchasesBills');
            Route::post('PurchasesBillsFilter', 'App\Http\Controllers\NewReportController@PurchasesBillsFilter');
            Route::get('PurchasesBillsFilterNeww', 'App\Http\Controllers\NewReportController@PurchasesBillsFilterNeww');
            Route::get('SaveDefaultColumnsPurchasesBills', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsPurchasesBills');
            Route::get('PurchasesBillsReportPrint/{type}', 'App\Http\Controllers\NewReportController@PurchasesBillsReportPrint');


        //StoresTransferReport
             Route::get('StoresTransferReport', 'App\Http\Controllers\NewReportController@StoresTransferReport');
           Route::post('StoresTransferReportFilter', 'App\Http\Controllers\NewReportController@StoresTransferReportFilter');
           Route::get('StoresTransferReportFilterNeww', 'App\Http\Controllers\NewReportController@StoresTransferReportFilterNeww');
            Route::get('SaveDefaultColumnsStoresTransferReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsStoresTransferReport');
            Route::get('StoresTransferReportPrint', 'App\Http\Controllers\NewReportController@StoresTransferReportPrint');

        //SafesTransferReport
            Route::get('SafesTransferReport', 'App\Http\Controllers\NewReportController@SafesTransferReport');
           Route::post('SafesTransferReportFilter', 'App\Http\Controllers\NewReportController@SafesTransferReportFilter');
           Route::get('SafesTransferReportFilterNeww', 'App\Http\Controllers\NewReportController@SafesTransferReportFilterNeww');
            Route::get('SaveDefaultColumnsSafesTransferReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsSafesTransferReport');
            Route::get('SafesTransferReportPrint', 'App\Http\Controllers\NewReportController@SafesTransferReportPrint');


        //StoresMovesReport
         Route::get('StoresMovesReport', 'App\Http\Controllers\NewReportController@StoresMovesReport');
           Route::post('StoresMovesReportFilter', 'App\Http\Controllers\NewReportController@StoresMovesReportFilter');
           Route::get('StoresMovesReportFilterNeww', 'App\Http\Controllers\NewReportController@StoresMovesReportFilterNeww');
            Route::get('SaveDefaultColumnsStoresMovesReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsStoresMovesReport');
            Route::get('StoresMovesReportPrint', 'App\Http\Controllers\NewReportController@StoresMovesReportPrint');


        //ProductMoveDetails
                Route::get('ProductMoveDetails', 'App\Http\Controllers\NewReportController@ProductMoveDetails');
             Route::post('ProductMoveDetailsFilter', 'App\Http\Controllers\NewReportController@ProductMoveDetailsFilter');
             Route::get('ProductMoveDetailsFilterNeww', 'App\Http\Controllers\NewReportController@ProductMoveDetailsFilterNeww');
            Route::get('SaveDefaultColumnsProductMoveDetails', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsProductMoveDetails');
            Route::get('ProductMoveDetailsPrint', 'App\Http\Controllers\NewReportController@ProductMoveDetailsPrint');


        //CompareSalesPrice
            Route::get('CompareSalesPrice', 'App\Http\Controllers\NewReportController@CompareSalesPrice');
            Route::post('CompareSalesPriceFilter', 'App\Http\Controllers\NewReportController@CompareSalesPriceFilter');
            Route::get('CompareSalesPriceFilterNeww', 'App\Http\Controllers\NewReportController@CompareSalesPriceFilterNeww');
            Route::get('SaveDefaultColumnsCompareSalesPrice', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsCompareSalesPrice');
            Route::get('CompareSalesPricePrint', 'App\Http\Controllers\NewReportController@CompareSalesPricePrint');


                //ProfitSalesProduct
            Route::get('ProfitSalesProduct', 'App\Http\Controllers\NewReportController@ProfitSalesProduct');
            Route::post('ProfitSalesProductFilter', 'App\Http\Controllers\NewReportController@ProfitSalesProductFilter');
            Route::get('ProfitSalesProductFilterNeww', 'App\Http\Controllers\NewReportController@ProfitSalesProductFilterNeww');
            Route::get('SaveDefaultColumnsProfitSalesProduct', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsProfitSalesProduct');
            Route::get('ProfitSalesProductPrint', 'App\Http\Controllers\NewReportController@ProfitSalesProductPrint');

            //ClientAccountStatement
         Route::get('ClientAccountStatement', 'App\Http\Controllers\NewReportController@ClientAccountStatement');
          Route::post('ClientAccountStatementFilter', 'App\Http\Controllers\NewReportController@ClientAccountStatementFilter');
          Route::get('ClientAccountStatementFilterNeww', 'App\Http\Controllers\NewReportController@ClientAccountStatementFilterNeww');
             Route::get('ClientAccountStatement/SaveDefaultClientStatement', 'App\Http\Controllers\NewReportController@SaveDefaultClientStatement');
             Route::get('SaveDefaultClientStatement', 'App\Http\Controllers\NewReportController@SaveDefaultClientStatement');
            Route::get('ClientAccountStatementPrint', 'App\Http\Controllers\NewReportController@ClientAccountStatementPrint');

        //MostSalesProducts
           Route::get('MostSalesProducts', 'App\Http\Controllers\NewReportController@MostSalesProducts');
            Route::post('MostSalesProductsFilter', 'App\Http\Controllers\NewReportController@MostSalesProductsFilter');
            Route::get('MostSalesProductsFilterNeww', 'App\Http\Controllers\NewReportController@MostSalesProductsFilterNeww');
            Route::get('SaveDefaultColumnsMostSalesProducts', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsMostSalesProducts');
            Route::get('MostSalesProductsPrint', 'App\Http\Controllers\NewReportController@MostSalesProductsPrint');


        //VendorAccountStatement
          Route::get('VendorAccountStatement', 'App\Http\Controllers\NewReportController@VendorAccountStatement');
          Route::post('VendorAccountStatementFilter', 'App\Http\Controllers\NewReportController@VendorAccountStatementFilter');
          Route::get('VendorAccountStatementFilterNeww', 'App\Http\Controllers\NewReportController@VendorAccountStatementFilterNeww');
            Route::get('SaveDefaultColumnsVendorAccountStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsVendorAccountStatement');
            Route::get('VendorAccountStatementPrint', 'App\Http\Controllers\NewReportController@VendorAccountStatementPrint');



          //ClientsStatement
           Route::get('ClientsStatement', 'App\Http\Controllers\NewReportController@ClientsStatement');
          Route::post('ClientsStatementFilter', 'App\Http\Controllers\NewReportController@ClientsStatementFilter');
          Route::get('ClientsStatementFilterNew', 'App\Http\Controllers\NewReportController@ClientsStatementFilterNew');
            Route::get('SaveDefaultColumnsClientsStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsClientsStatement');
            Route::get('ClientsStatementPrint', 'App\Http\Controllers\NewReportController@ClientsStatementPrint');

          //VendorsStatement
            Route::get('VendorsStatement', 'App\Http\Controllers\NewReportController@VendorsStatement');
          Route::post('VendorsStatementFilter', 'App\Http\Controllers\NewReportController@VendorsStatementFilter');
          Route::get('VendorsStatementFilterNeww', 'App\Http\Controllers\NewReportController@VendorsStatementFilterNeww');
            Route::get('SaveDefaultColumnsVendorsStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsVendorsStatement');
            Route::get('VendorsStatementPrint', 'App\Http\Controllers\NewReportController@VendorsStatementPrint');


        //ExpensesList
            Route::get('ExpensesList', 'App\Http\Controllers\NewReportController@ExpensesList');
            Route::post('ExpensesListFilter', 'App\Http\Controllers\NewReportController@ExpensesListFilter');
            Route::get('ExpensesListFilterNeww', 'App\Http\Controllers\NewReportController@ExpensesListFilterNeww');
            Route::get('ExpensesListFilterNewwPrint', 'App\Http\Controllers\NewReportController@ExpensesListFilterNewwPrint');
            Route::get('SaveDefaultColumnsExpensesList', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsExpensesList');
            Route::get('ExpensesListPrint', 'App\Http\Controllers\NewReportController@ExpensesListPrint');


        //IncomListReport
              Route::get('IncomListReport', 'App\Http\Controllers\NewReportController@IncomListReport');
            Route::post('IncomListReportFilter', 'App\Http\Controllers\NewReportController@IncomListReportFilter');
            Route::get('IncomListReportFilterNeww', 'App\Http\Controllers\NewReportController@IncomListReportFilterNeww');
      Route::get('IncomListReportPrint', 'App\Http\Controllers\NewReportController@IncomListReportPrint');
             //StoresCostNew
        Route::get('StoresCostNew', 'App\Http\Controllers\NewReportController@StoresCostNew');
            Route::get('StoresCostNewFilter', 'App\Http\Controllers\NewReportController@StoresCostNewFilter');
  Route::get('StoresCostNewPrint', 'App\Http\Controllers\NewReportController@StoresCostNewPrint');


        //SubIncomList
            Route::get('SubIncomList', 'App\Http\Controllers\NewReportController@SubIncomList');
            Route::get('SubIncomListFilter', 'App\Http\Controllers\NewReportController@SubIncomListFilter');

      //EmpGoals
            Route::get('EmpGoals', 'App\Http\Controllers\NewReportController@EmpGoals');
    Route::post('EmpGoalsFilter', 'App\Http\Controllers\NewReportController@EmpGoalsFilter');
    Route::get('EmpGoalsFilterNeww', 'App\Http\Controllers\NewReportController@EmpGoalsFilterNeww');

        //InventorySerial
              Route::get('InventorySerial', 'App\Http\Controllers\NewReportController@InventorySerial');
    Route::post('InventorySerialFilter', 'App\Http\Controllers\NewReportController@InventorySerialFilter');
    Route::get('InventorySerialFilterNeww', 'App\Http\Controllers\NewReportController@InventorySerialFilterNeww');


        //StoresBalancesNew
              Route::get('StoresBalancesNew', 'App\Http\Controllers\NewReportController@StoresBalancesNew');
            Route::post('StoresBalancesNewFilter', 'App\Http\Controllers\NewReportController@StoresBalancesNewFilter');
            Route::get('StoresBalancesNewFilterNeww', 'App\Http\Controllers\NewReportController@StoresBalancesNewFilterNeww');
  Route::get('StoresBalancesNewPrint', 'App\Http\Controllers\NewReportController@StoresBalancesNewPrint');

        //StoresBalancesCat
                     Route::get('StoresBalancesCatNew', 'App\Http\Controllers\NewReportController@StoresBalancesCat');
            Route::post('StoresBalancesCatFilter', 'App\Http\Controllers\NewReportController@StoresBalancesCatFilter');
            Route::get('StoresBalancesCatFilterNeww', 'App\Http\Controllers\NewReportController@StoresBalancesCatFilterNeww');
  Route::get('StoresBalancesCatPrint', 'App\Http\Controllers\NewReportController@StoresBalancesCatPrint');


        //StoresInventoryNew
                             Route::get('StoresInventoryNew', 'App\Http\Controllers\NewReportController@StoresInventoryNew');
            Route::post('StoresInventoryNewFilter', 'App\Http\Controllers\NewReportController@StoresInventoryNewFilter');
            Route::get('StoresInventoryNewFilterNeww', 'App\Http\Controllers\NewReportController@StoresInventoryNewFilterNeww');
  Route::get('StoresInventoryNewPrint', 'App\Http\Controllers\NewReportController@StoresInventoryNewPrint');


        //ItemCost
                 Route::get('ItemCost', 'App\Http\Controllers\NewReportController@ItemCost');
            Route::post('ItemCostNewFilter', 'App\Http\Controllers\NewReportController@ItemCostNewFilter');
            Route::get('ItemCostNewFilterNeww', 'App\Http\Controllers\NewReportController@ItemCostNewFilterNeww');


        //DelegateSalesDetails
            Route::get('DelegateSalesDetailss', 'App\Http\Controllers\NewReportController@DelegateSalesDetailss');
            Route::post('DelegateSalesDetailssFilter', 'App\Http\Controllers\NewReportController@DelegateSalesDetailssFilter');
            Route::get('DelegateSalesDetailssFilterNeww', 'App\Http\Controllers\NewReportController@DelegateSalesDetailssFilterNeww');
            Route::get('DelegateSalesDetailssFilterPrint', 'App\Http\Controllers\NewReportController@DelegateSalesDetailssFilterPrint');

           //ProfitDelegateSalesDetails
            Route::get('ProfitDelegateSalesDetails', 'App\Http\Controllers\NewReportController@ProfitDelegateSalesDetails');
            Route::post('ProfitDelegateSalesDetailsFilter', 'App\Http\Controllers\NewReportController@ProfitDelegateSalesDetailsFilter');
            Route::get('ProfitDelegateSalesDetailsFilterNeww', 'App\Http\Controllers\NewReportController@ProfitDelegateSalesDetailsFilterNeww');


            //StoresCosts
            Route::get('StoresCosts', 'App\Http\Controllers\NewReportController@StoresCosts');
            Route::post('StoresCostsFilter', 'App\Http\Controllers\NewReportController@StoresCostsFilter');
            Route::get('StoresCostsFilterNeww', 'App\Http\Controllers\NewReportController@StoresCostsFilterNeww');



                //DailyClosingDetails
            Route::get('DailyClosingDetails', 'App\Http\Controllers\NewReportController@DailyClosingDetails');
            Route::get('DailyClosingDetailsFilter', 'App\Http\Controllers\NewReportController@DailyClosingDetailsFilter');

            Route::get('FilterSalesDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterSalesDailyClosingDetails');
            Route::get('FilterRSalesDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterRSalesDailyClosingDetails');
            Route::get('FilterSettlement3agzDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterSettlement3agzDailyClosingDetails');
            Route::get('FilterPetrolDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterPetrolDailyClosingDetails');
            Route::get('FilterShippingDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterShippingDailyClosingDetails');
            Route::get('FilterMaintainceDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterMaintainceDailyClosingDetails');
            Route::get('FilterSettlementZyadaDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterSettlementZyadaDailyClosingDetails');
            Route::get('FilterRMaintainceDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterRMaintainceDailyClosingDetails');
            Route::get('FilterPurchasesDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterPurchasesDailyClosingDetails');
            Route::get('FilterRPurchasesDailyClosingDetails', 'App\Http\Controllers\NewReportController@FilterRPurchasesDailyClosingDetails');

            Route::get('DailyClosingDetailsFilterPrint', 'App\Http\Controllers\NewReportController@DailyClosingDetailsFilterPrint');



        //InstallmentCompaniesSales

            Route::get('InstallmentCompaniesSales', 'App\Http\Controllers\NewReportController@InstallmentCompaniesSales');
          Route::post('InstallmentCompaniesSalesFilter', 'App\Http\Controllers\NewReportController@InstallmentCompaniesSalesFilter');
          Route::get('InstallmentCompaniesSalesFilterNeww', 'App\Http\Controllers\NewReportController@InstallmentCompaniesSalesFilterNeww');
            Route::get('SaveDefaultColumnsInstallmentCompaniesSales', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsInstallmentCompaniesSales');


        //Customer_Debts
        Route::get('Customer_Debts', 'App\Http\Controllers\NewReportController@Customer_Debts');
        Route::get('Customer_DebtsFilterTwo', 'App\Http\Controllers\NewReportController@Customer_DebtsFilterTwo');
        //Vendor_Debts
        Route::get('Vendor_Debts', 'App\Http\Controllers\NewReportController@Vendor_Debts');
        Route::get('Vendor_DebtsFilterTwo', 'App\Http\Controllers\NewReportController@Vendor_DebtsFilterTwo');


        //SalesProsMoreDetails
               Route::get('SalesProsMoreDetails', 'App\Http\Controllers\NewReportController@SalesProsMoreDetails');
               Route::get('SalesProsMoreDetailsFilter', 'App\Http\Controllers\NewReportController@SalesProsMoreDetailsFilter');

        //StagnantItemsTwo
  Route::get('StagnantItemsTwo', 'App\Http\Controllers\NewReportController@StagnantItemsTwo');
               Route::get('StagnantItemsTwoFilter', 'App\Http\Controllers\NewReportController@StagnantItemsTwoFilter');

        //SalesCustomersGroups
         Route::get('SalesCustomersGroups', 'App\Http\Controllers\NewReportController@SalesCustomersGroups');
         Route::post('SalesCustomersGroupsFilter', 'App\Http\Controllers\NewReportController@SalesCustomersGroupsFilter');
         Route::get('SalesCustomersGroupsFilterNeww', 'App\Http\Controllers\NewReportController@SalesCustomersGroupsFilterNeww');




        //Filters
          Route::get('BranchReportStoresFilter', 'App\Http\Controllers\NewReportController@BranchReportStoresFilter');
          Route::get('BranchReportSafesFilter', 'App\Http\Controllers\NewReportController@BranchReportSafesFilter');
          Route::get('ClientGroupsFilter', 'App\Http\Controllers\NewReportController@ClientGroupsFilter');
          Route::get('GroupSingleFilter', 'App\Http\Controllers\NewReportController@GroupSingleFilter');
          Route::get('SubAccountFilter', 'App\Http\Controllers\NewReportController@SubAccountFilter');



        //===========================================================







//===== End Reports =======================================================================================================


//===== Maintenance =======================================================================================================

                 //Manufactur Company
                 Route::get('ManufacturCompany', 'App\Http\Controllers\MaintenanceController@ManufacturCompanyPage');
                 Route::post('AddManufacturCompany', 'App\Http\Controllers\MaintenanceController@AddManufacturCompany');
                 Route::post('EditManufacturCompany/{id}', 'App\Http\Controllers\MaintenanceController@EditManufacturCompany');
                 Route::get('DeleteManufacturCompany/{id}', 'App\Http\Controllers\MaintenanceController@DeleteManufacturCompany');


                 //Devices Types
                 Route::get('DevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@DevicesTypesPage');
                 Route::post('AddDevicesTypes', 'App\Http\Controllers\MaintenanceController@AddDevicesTypes');
                 Route::post('EditDevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@EditDevicesTypes');
                 Route::get('DeleteDevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDevicesTypes');

                 //Desvice Cases
                 Route::get('DesviceCases', 'App\Http\Controllers\MaintenanceController@DesviceCasesPage');
                 Route::post('AddDesviceCases', 'App\Http\Controllers\MaintenanceController@AddDesviceCases');
                 Route::post('EditDesviceCases/{id}', 'App\Http\Controllers\MaintenanceController@EditDesviceCases');
                 Route::get('DeleteDesviceCases/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDesviceCases');

                 //RefuseReasons
                 Route::get('RefuseReasons', 'App\Http\Controllers\MaintenanceController@RefuseReasonsPage');
                 Route::post('AddRefuseReasons', 'App\Http\Controllers\MaintenanceController@AddRefuseReasons');
                 Route::post('EditRefuseReasons/{id}', 'App\Http\Controllers\MaintenanceController@EditRefuseReasons');
                 Route::get('DeleteRefuseReasons/{id}', 'App\Http\Controllers\MaintenanceController@DeleteRefuseReasons');

                 //Faults Type
                 Route::get('FaultsType', 'App\Http\Controllers\MaintenanceController@FaultsTypePage');
                 Route::post('AddFaultsType', 'App\Http\Controllers\MaintenanceController@AddFaultsType');
                 Route::post('EditFaultsType/{id}', 'App\Http\Controllers\MaintenanceController@EditFaultsType');
                 Route::get('DeleteFaultsType/{id}', 'App\Http\Controllers\MaintenanceController@DeleteFaultsType');
    //ReciptMaintaince
Route::get('ReciptMaintaince', 'App\Http\Controllers\MaintenanceController@ReciptMaintaincePage');
Route::get('FilterReciptBills', 'App\Http\Controllers\MaintenanceController@FilterReciptBills');
Route::get('FilterMainBills', 'App\Http\Controllers\MaintenanceController@FilterMainBills');
Route::get('CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::post('AddReciptMaintaince', 'App\Http\Controllers\MaintenanceController@AddReciptMaintaince');
Route::get('ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('ReciptMaintaincePrint/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintaincePrint');
Route::get('ReciptMaintainceSechdule', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceSechdulePage');
Route::get('EditReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@EditReciptMaintaince');
Route::get('DeleteReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@DeleteReciptMaintaince');
Route::post('PostEditReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@PostEditReciptMaintaince');
Route::get('EditReciptMaintaince/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('EditReciptMaintaince/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('EditReciptMaintaince/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('EditReciptMaintaince/{id}/OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
Route::get('SureReciptMain/{id}', 'App\Http\Controllers\MaintenanceController@SureReciptMain');
Route::get('RefuseReciptMain/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMain');
Route::post('RefuseReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMaintaince');
Route::post('TransferReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@TransferReciptMaintaince');
Route::get('ReciptMaintainceSechduleEng', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceSechduleEng');
Route::get('ReportClientMain/{id}', 'App\Http\Controllers\MaintenanceController@ReportClientMain');
Route::post('RefuseReciptMaintainceClient/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMaintainceClient');
Route::get('SureMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@SureMaintainceBill');
Route::post('PostSureMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostSureMaintainceBill');
Route::get('OpenMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@OpenMaintainceBill');
Route::get('EditReciptMaintainceEng/{id}', 'App\Http\Controllers\MaintenanceController@EditReciptMaintainceEng');
Route::get('SureMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('SureMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('SureMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('OpenMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('OpenMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('OpenMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('ReciptMaintanceTalf/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');

Route::post('PostOpenMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostOpenMaintainceBill');
Route::get('EditReciptMaintainceEng/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('EditReciptMaintainceEng/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('EditReciptMaintainceEng/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('EditReciptMaintainceEng/{id}/OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
Route::post('PostEditReciptMaintainceEng/{id}', 'App\Http\Controllers\MaintenanceController@PostEditReciptMaintainceEng');
Route::get('ReturnMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('ReturnMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('ReturnMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('ReturnMaintainceBill/{id}/AvaliableQtyMaintanceFilter', 'App\Http\Controllers\MaintenanceController@AvaliableQtyMaintanceFilter');
Route::post('SureMaintainceBillForm/{id}', 'App\Http\Controllers\MaintenanceController@SureMaintainceBillForm');
Route::get('ReciptMaintanceTalf/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintanceTalf');
Route::post('PostSureMaintainceBillTalf', 'App\Http\Controllers\MaintenanceController@PostSureMaintainceBillTalf');
    //Return Maintaince
Route::get('ReturnMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@ReturnMaintainceBillPage');
Route::post('PostReturnMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostReturnMaintainceBill');
Route::get('ReturnMaintainceBillSechdule', 'App\Http\Controllers\MaintenanceController@ReturnMaintainceBillSechdule');
Route::get('ReturnMaintaincePrint/{id}', 'App\Http\Controllers\MaintenanceController@ReturnMaintaincePrint');
Route::get('FilterReturnMainBills', 'App\Http\Controllers\MaintenanceController@FilterReturnMainBills');



    //MaintainceBill
    Route::get('MaintainceBill', 'App\Http\Controllers\MaintenanceController@MaintainceBillPage');
    Route::get('ErrorsFilter', 'App\Http\Controllers\MaintenanceController@ErrorsFilter');
    Route::get('ReciptMaintainceNumberFilter/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceNumberFilter');
    Route::get('MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
    Route::get('NewMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@NewMaintainceProductsFilter');
    Route::get('OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
    Route::post('AddMaintainceBill', 'App\Http\Controllers\MaintenanceController@AddMaintainceBill');
    Route::get('MaintenanceBillPrint/{id}', 'App\Http\Controllers\MaintenanceController@MaintenanceBillPrint');
    Route::get('MaintainceBillSechdule', 'App\Http\Controllers\MaintenanceController@MaintainceBillSechdule');

                       //Device Descrips
                Route::get('DeviceDescrips', 'App\Http\Controllers\MaintenanceController@DeviceDescripsPage');
                Route::post('AddDeviceDescrips', 'App\Http\Controllers\MaintenanceController@AddDeviceDescrips');
                Route::post('EditDeviceDescrips/{id}', 'App\Http\Controllers\MaintenanceController@EditDeviceDescrips');
                Route::get('DeleteDeviceDescrips/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDeviceDescrips');

                //Terms Maintaince
                Route::get('TermsMaintaince', 'App\Http\Controllers\MaintenanceController@TermsMaintaincePage');
      //Add Terms Maintance
          Route::post('AddTermsMaintaince', 'App\Http\Controllers\MaintenanceController@AddTermsMaintaince');



                //MaintainceColors
                Route::get('MaintainceColors', 'App\Http\Controllers\MaintenanceController@MaintainceColorsPage');
                Route::post('AddMaintainceColors', 'App\Http\Controllers\MaintenanceController@AddMaintainceColors');

 //=====  End Maintenance ===================================================================================================


//=====  Manufacturing ===================================================================================================


    //Manufacturing Halls
    Route::get('ManufacturingHalls', 'App\Http\Controllers\ManufacturingController@ManufacturingHallsPage');
    Route::post('AddManufacturingHalls', 'App\Http\Controllers\ManufacturingController@AddManufacturingHalls');
    Route::post('EditManufacturingHalls/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingHalls');
    Route::get('DeleteManufacturingHalls/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingHalls');

    //ManufacturingModel
       Route::get('ManufacturingModel', 'App\Http\Controllers\ManufacturingController@ManufacturingModelPage');
       Route::post('AddManufacturingModel', 'App\Http\Controllers\ManufacturingController@AddManufacturingModel');
       Route::get('IncomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilter');
       Route::get('OutcomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilter');

       Route::get('EditManufacturingModel/IncomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilter');
       Route::get('EditManufacturingModel/OutcomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilter');

       Route::get('ManuExecution/{id}', 'App\Http\Controllers\ManufacturingController@ManuExecutionPage');
       Route::get('PrintManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@PrintManufacturingModel');



       //Sechdule
       Route::get('ManufacturingModelSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingModelSechdule');
       Route::get('EditManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingModel');
       Route::get('EditManufacturingModelPrecent/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingModelPrecent');
       Route::get('DeleteManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingModel');
       Route::post('PostEditManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingModel');
       Route::post('PostEditManufacturingModelPrecent/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingModelPrecent');


       //ManufacturingModelPrecent
       Route::get('ManufacturingModelPrecent', 'App\Http\Controllers\ManufacturingController@ManufacturingModelPrecentPage');
       Route::get('OutcomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilterPrecent');
       Route::get('EditManufacturingModelPrecent/OutcomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilterPrecent');
       Route::get('IncomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilterPrecent');
       Route::get('EditManufacturingModelPrecent/IncomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilterPrecent');
       Route::post('AddManufacturingModelPrecent', 'App\Http\Controllers\ManufacturingController@AddManufacturingModelPrecent');


       //ExecutingandReceiving
       Route::get('ExecutingandReceiving', 'App\Http\Controllers\ManufacturingController@ExecutingandReceiving');
       Route::get('ModelExecutingFilter', 'App\Http\Controllers\ManufacturingController@ModelExecutingFilter');
       Route::get('ManuExecution/{id}/ModelExecutingFilter', 'App\Http\Controllers\ManufacturingController@ModelExecutingFilter');
       Route::post('AddExecutingReceiving', 'App\Http\Controllers\ManufacturingController@AddExecutingReceiving');


       //ManufacturingOrder
       Route::get('ManufacturingOrderSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderSechdule');
       Route::get('ManufacturingOrder', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderPage');
       Route::get('ManufacturingOrderFilter', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderFilter');
       Route::post('AddManufacturingOrder', 'App\Http\Controllers\ManufacturingController@AddManufacturingOrder');
       Route::get('DeleteManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingOrder');
       Route::get('EditManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingOrderPage');
       Route::post('PostEditManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingOrder');
       Route::post('ExchangeGoodsManufacturingOrder', 'App\Http\Controllers\ManufacturingController@ExchangeGoodsManufacturingOrder');
       Route::get('ManufacturingExchangeGoodsPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExchangeGoodsPrint');


       Route::get('EditManufacturingOrder/ClientPhoneAndAddress/{id}', 'App\Http\Controllers\ManufacturingController@ClientPhoneAndAddress');
       Route::get('EditManufacturingOrder/DelegatePhone/{id}', 'App\Http\Controllers\ManufacturingController@DelegatePhone');


       //Manufacturing_Request
       Route::get('Manufacturing_Request_Sechdule', 'App\Http\Controllers\ManufacturingController@Manufacturing_Request_Sechdule');
       Route::get('Manufacturing_Request', 'App\Http\Controllers\ManufacturingController@Manufacturing_RequestPage');
       Route::get('ManufacturingRequestFilter', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestFilter');
       Route::get('ClientPhoneAndAddress/{id}', 'App\Http\Controllers\ManufacturingController@ClientPhoneAndAddress');
       Route::get('DelegatePhone/{id}', 'App\Http\Controllers\ManufacturingController@DelegatePhone');
       Route::get('ManufacturingRequestPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestPrint');
       Route::post('AddManufacturingRequest', 'App\Http\Controllers\ManufacturingController@AddManufacturingRequest');

       Route::get('ApproveManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@ApproveManufacturingRequest');
       Route::get('EndManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@EndManufacturingRequest');
       Route::get('DeleteManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingRequest');
       Route::get('EditManufacturingRequest', 'App\Http\Controllers\ManufacturingController@EditManufacturingRequest');
       Route::get('ManufacturingRequestTransferToOrder', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestTransferToOrder');
       Route::post('PostEditManufacturingRequest', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingRequest');
       Route::post('AddTransferManufacturingOrder', 'App\Http\Controllers\ManufacturingController@AddTransferManufacturingOrder');


       //ExchangeManufacturingGoodsSechdule
       Route::get('ExchangeManufacturingGoodsSechdule', 'App\Http\Controllers\ManufacturingController@ExchangeManufacturingGoodsSechdule');
       Route::post('SureExchangeGoodsManufacturingOrder', 'App\Http\Controllers\ManufacturingController@SureExchangeGoodsManufacturingOrder');

       //ManufacturingExecution
       Route::get('ManufacturingExecution/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionPage');
       Route::post('AddManufacturingExecution', 'App\Http\Controllers\ManufacturingController@AddManufacturingExecution');
       Route::get('ManufacturingExecutionPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionPrint');
       Route::get('ManufacturingExecutionSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionSechdule');
       Route::get('DeleteManufacturingExecution/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingExecution');



       //ExaminationsTypes
       Route::get('ExaminationsTypes', 'App\Http\Controllers\ManufacturingController@ExaminationsTypesPage');
       Route::post('AddExaminationsTypes', 'App\Http\Controllers\ManufacturingController@AddExaminationsTypes');
       Route::post('EditExaminationsTypes/{id}', 'App\Http\Controllers\ManufacturingController@EditExaminationsTypes');
       Route::get('DeleteExaminationsTypes/{id}', 'App\Http\Controllers\ManufacturingController@DeleteExaminationsTypes');

       //Quality
       Route::get('ManufacturingExecutionQuality', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionQuality');
       Route::get('QualitySechdule', 'App\Http\Controllers\ManufacturingController@QualitySechdule');
       Route::get('ExmineFilter/{id}', 'App\Http\Controllers\ManufacturingController@ExmineFilter');
       Route::get('QualityPrint/{id}', 'App\Http\Controllers\ManufacturingController@QualityPrintPage');
       Route::get('DeleteQuality/{id}', 'App\Http\Controllers\ManufacturingController@DeleteQuality');
       Route::get('QualityDone/{id}', 'App\Http\Controllers\ManufacturingController@QualityDone');
       Route::post('AddQuality', 'App\Http\Controllers\ManufacturingController@AddQuality');


 //=====  End Manufacturing ==================================================================================================





// === Owner Reports ========================================================================================================

    //User Log
    Route::get('UserLog', 'App\Http\Controllers\OwnerController@UserLogPage');
    Route::get('UserLogFilter', 'App\Http\Controllers\OwnerController@UserLogFilter');


    //EmpLocations
     Route::get('EmpLocations', 'App\Http\Controllers\OwnerController@EmpLocationsPage');

    //Orders
        Route::get('Orders', 'App\Http\Controllers\OwnerController@OrdersPage');
        Route::get('PendingSales/{id}', 'App\Http\Controllers\OwnerController@PendingSales');
        Route::get('RecivedShipCompSales/{id}', 'App\Http\Controllers\OwnerController@RecivedShipCompSales');
        Route::get('RecivedClientSales/{id}', 'App\Http\Controllers\OwnerController@RecivedClientSales');
        Route::get('PendingPurch/{id}', 'App\Http\Controllers\OwnerController@PendingPurch');
        Route::get('RecivedShipCompPurch/{id}', 'App\Http\Controllers\OwnerController@RecivedShipCompPurch');
        Route::get('RecivedClientPurch/{id}', 'App\Http\Controllers\OwnerController@RecivedClientPurch');

// === End Owner Reports ===================================================================================================


// === Capital ============================================================================================================

    //Capital
    Route::get('Capital', 'App\Http\Controllers\CapitalController@CapitalPage');
    Route::post('AddCapital', 'App\Http\Controllers\CapitalController@AddCapital');

    //Partners
    Route::get('Partners', 'App\Http\Controllers\CapitalController@PartnersPage');
    Route::post('AddPartner', 'App\Http\Controllers\CapitalController@AddPartner');
    Route::get('DeletePartner/{id}', 'App\Http\Controllers\CapitalController@DeletePartner');

    //Spend_Profits
    Route::get('Spend_Profits', 'App\Http\Controllers\CapitalController@Spend_ProfitsPage');
    Route::get('PartnerFilter/{id}', 'App\Http\Controllers\CapitalController@PartnerFilter');
    Route::get('PrintSpendProfits/{id}', 'App\Http\Controllers\CapitalController@PrintSpendProfits');
    Route::post('AddSpendProfit', 'App\Http\Controllers\CapitalController@AddSpendProfit');

    //Branches
    Route::get('Branches', 'App\Http\Controllers\CapitalController@BranchesPage');
    Route::post('AddBranches', 'App\Http\Controllers\CapitalController@AddBranches');
    Route::post('EditBranches/{id}', 'App\Http\Controllers\CapitalController@EditBranches');
    Route::get('DeleteBranches/{id}', 'App\Http\Controllers\CapitalController@DeleteBranches');
// === End Capital ========================================================================================================


// === Secretariat ========================================================================================================

        //Secretariat_Stores
     Route::get('Secretariat_Stores', 'App\Http\Controllers\SecretariatController@Secretariat_StoresPage');
     Route::post('AddSecretariat_Stores', 'App\Http\Controllers\SecretariatController@AddSecretariat_Stores');
     Route::post('EditSecretariat_Stores/{id}', 'App\Http\Controllers\SecretariatController@EditSecretariat_Stores');
     Route::get('DeleteSecretariat_Stores/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Stores');

        //Secretariat_Import_goods
          Route::get('Secretariat_Import_goods', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goodsPage');
          Route::get('ImportGoodsProductsFilter', 'App\Http\Controllers\SecretariatController@ImportGoodsProductsFilter');
          Route::post('AddSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@AddSecretariat_Import_goods');
          Route::get('Secretariat_Import_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goodsPrint');
          Route::get('Secretariat_Import_goods_Sechdule', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goods_Sechdule');
          Route::get('EditSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@EditSecretariat_Import_goods');
          Route::get('DeleteSecretariat_Import_goods/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Import_goods');
          Route::post('PostEditSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@PostEditSecretariat_Import_goods');

        //Secretariat_Export_goods
          Route::get('Secretariat_Export_goods', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goodsPage');
          Route::get('ExportProductsFilter', 'App\Http\Controllers\SecretariatController@ExportProductsFilter');
          Route::post('AddSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@AddSecretariatExportGoods');
          Route::get('Secretariat_Export_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goodsPrint');
          Route::get('Secretariat_Export_goods_Sechdule', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goods_Sechdule');
          Route::get('EditSecretariat_Export_goods', 'App\Http\Controllers\SecretariatController@EditSecretariat_Export_goods');
          Route::get('DeleteSecretariat_Export_goods/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Export_goods');
          Route::post('PostEditSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@PostEditSecretariatExportGoods');
          //RecivedSecretariat_Export_goods
          Route::get('RecivedSecretariat_Export_goods', 'App\Http\Controllers\SecretariatController@RecivedSecretariat_Export_goods');
          Route::post('PostRecivedSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@PostRecivedSecretariatExportGoods');
          Route::get('Secretariat_Recived_Export_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Recived_Export_goodsPrint');


          //Secretariat_Stores_Qty
          Route::get('Secretariat_Stores_Qty', 'App\Http\Controllers\SecretariatController@Secretariat_Stores_Qty');
          Route::get('Secretariat_Stores_QtyFilter', 'App\Http\Controllers\SecretariatController@Secretariat_Stores_QtyFilter');

        //ManufacturingModelSecretariat
         Route::get('ManufacturingModelSecretariatSechdule', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariatSechdule');
         Route::get('ManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariat');
       Route::post('AddManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@AddManufacturingModelSecretariat');
       Route::get('IncomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilter');
       Route::get('EditManufacturingSecretariatModel/IncomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilter');
       Route::get('OutcomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilter');
       Route::get('EditManufacturingSecretariatModel/OutcomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilter');
       Route::get('DeleteManufacturingModelSecretariat/{id}', 'App\Http\Controllers\SecretariatController@DeleteManufacturingModelSecretariat');
       Route::get('EditManufacturingSecretariatModel/{id}', 'App\Http\Controllers\SecretariatController@EditManufacturingSecretariatModel');
       Route::post('PostEditManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@PostEditManufacturingModelSecretariat');
       Route::get('ManuSecrtaritExecution/{id}', 'App\Http\Controllers\SecretariatController@ManuSecrtaritExecution');


        //ManufacturingModelSecretariatPrecent
           Route::get('ManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariatPrecent');
           Route::post('AddManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@AddManufacturingModelSecretariatPrecent');
           Route::get('IncomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilterPrecent');
           Route::get('OutcomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilterPrecent');

            Route::get('EditManufacturingModelSecretariatPrecent/IncomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilterPrecent');
            Route::get('EditManufacturingModelSecretariatPrecent/OutcomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilterPrecent');

           Route::get('EditManufacturingModelSecretariatPrecent/{id}', 'App\Http\Controllers\SecretariatController@EditManufacturingModelSecretariatPrecent');
              Route::post('PostEditManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@PostEditManufacturingModelSecretariatPrecent');

        //ExecutingReceivingSecretariat
             Route::get('ExecutingReceivingSecretariat', 'App\Http\Controllers\SecretariatController@ExecutingReceivingSecretariat');
             Route::get('ModelExecutingReceivingSecretariatFilter', 'App\Http\Controllers\SecretariatController@ModelExecutingReceivingSecretariatFilter');
             Route::get('ManuSecrtaritExecution/{id}/ModelExecutingReceivingSecretariatFilter', 'App\Http\Controllers\SecretariatController@ModelExecutingReceivingSecretariatFilter');
             Route::post('AddExecutingReceivingSecretariat', 'App\Http\Controllers\SecretariatController@AddExecutingReceivingSecretariat');


          // === End Secretariat ======================================================================================================

          // === Petrol =============================================================================================================

          // == CompanyCars ==
          Route::get('CompanyCars', 'App\Http\Controllers\PetrolController@CompanyCarsPage');
          Route::post('AddCompanyCars', 'App\Http\Controllers\PetrolController@AddCompanyCars');
          Route::post('EditCompanyCars/{id}', 'App\Http\Controllers\PetrolController@EditCompanyCars');
          Route::get('DeleteCompanyCars/{id}', 'App\Http\Controllers\PetrolController@DeleteCompanyCars');

          // == BonesType ==
          Route::get('BonesType', 'App\Http\Controllers\PetrolController@BonesTypePage');
          Route::post('AddBonesType', 'App\Http\Controllers\PetrolController@AddBonesType');
          Route::post('EditBonesType/{id}', 'App\Http\Controllers\PetrolController@EditBonesType');
          Route::get('DeleteBonesType/{id}', 'App\Http\Controllers\PetrolController@DeleteBonesType');

          // == ReciptsType ==
          Route::get('ReciptsType', 'App\Http\Controllers\PetrolController@ReciptsTypePage');
          Route::post('AddReciptsType', 'App\Http\Controllers\PetrolController@AddReciptsType');
          Route::post('EditReciptsType/{id}', 'App\Http\Controllers\PetrolController@EditReciptsType');
          Route::get('DeleteReciptsType/{id}', 'App\Http\Controllers\PetrolController@DeleteReciptsType');


          // == CountersType ==
          Route::get('CountersType', 'App\Http\Controllers\PetrolController@CountersTypePage');
          Route::post('AddCountersType', 'App\Http\Controllers\PetrolController@AddCountersType');
          Route::post('EditCountersType/{id}', 'App\Http\Controllers\PetrolController@EditCountersType');
          Route::get('DeleteCountersType/{id}', 'App\Http\Controllers\PetrolController@DeleteCountersType');

          //PurchasePetrol
          Route::get('PurchasePetrol', 'App\Http\Controllers\PetrolController@PurchasePetrolPage');
          Route::get('PurchacesPetrolFilter', 'App\Http\Controllers\PetrolController@PurchacesPetrolFilter');
          Route::post('AddPurchasesPetrol', 'App\Http\Controllers\PetrolController@AddPurchasesPetrol');
          Route::get('PurchPetrolPrint/{id}', 'App\Http\Controllers\PetrolController@PurchPetrolPrint');
          Route::get('PurchasePetrolSechdule', 'App\Http\Controllers\PetrolController@PurchasePetrolSechdule');
          Route::get('DeletePurchasePetrol/{id}', 'App\Http\Controllers\PetrolController@DeletePurchasePetrol');
          Route::get('EditPurchPetrol', 'App\Http\Controllers\PetrolController@EditPurchPetrolPage');
          Route::post('PostEditPurchasesPetrol', 'App\Http\Controllers\PetrolController@PostEditPurchasesPetrol');

          //SalesPetrol
          Route::get('SalesPetrol', 'App\Http\Controllers\PetrolController@SalesPetrolPage');
          Route::get('SalesPetrolSechdule', 'App\Http\Controllers\PetrolController@SalesPetrolSechdule');
          Route::get('SalesPetrolPrint/{id}', 'App\Http\Controllers\PetrolController@SalesPetrolPrint');
          Route::get('ConsumptionFilter', 'App\Http\Controllers\PetrolController@ConsumptionFilter');
          Route::get('CounterDataFilter', 'App\Http\Controllers\PetrolController@CounterDataFilter');
          Route::get('StoreCounterFilter', 'App\Http\Controllers\PetrolController@StoreCounterFilter');
          Route::post('AddSalesPetrol', 'App\Http\Controllers\PetrolController@AddSalesPetrol');
          Route::get('DeleteSalesPetrol/{id}', 'App\Http\Controllers\PetrolController@DeleteSalesPetrol');
          Route::get('EditSalesPetrol', 'App\Http\Controllers\PetrolController@EditSalesPetrol');
          Route::post('PostEditSalesPetrol', 'App\Http\Controllers\PetrolController@PostEditSalesPetrol');





// === End Petrol =========================================================================================================


// === Website =========================================================================================================


    //WebSlider
                    Route::get('WebSlider', 'App\Http\Controllers\WebsiteController@WebSliderPage');
                    Route::post('AddWebSlider', 'App\Http\Controllers\WebsiteController@AddWebSlider');
                    Route::post('EditWebSlider/{id}', 'App\Http\Controllers\WebsiteController@EditWebSlider');
                    Route::get('DeleteWebSlider/{id}', 'App\Http\Controllers\WebsiteController@DeleteWebSlider');
                    Route::get('UnActiveSlider/{id}', 'App\Http\Controllers\WebsiteController@UnActiveSlider');
                    Route::get('ActiveSlider/{id}', 'App\Http\Controllers\WebsiteController@ActiveSlider');






     //About
                    Route::get('About', 'App\Http\Controllers\WebsiteController@AboutPage');


       //SocialMedia
            Route::get('SocialMedia', 'App\Http\Controllers\WebsiteController@SocialMediaPage');
            Route::post('SocialMediaUpdate/{id}', 'App\Http\Controllers\WebsiteController@SocialMediaUpdate');


         //MsgRqst
                    Route::get('MsgRqst', 'App\Http\Controllers\WebsiteController@MsgRqstPage');
                    Route::get('DeleteMsgRqst/{id}', 'App\Http\Controllers\WebsiteController@DeleteMsgRqst');

            //ContactUS
            Route::get('ContactUS', 'App\Http\Controllers\WebsiteController@ContactUSPage');


        //Articles
                    Route::get('Articles', 'App\Http\Controllers\WebsiteController@ArticlesPage');
                    Route::get('DeleteArticles/{id}', 'App\Http\Controllers\WebsiteController@DeleteArticles');

         //Polices
                    Route::get('Polices', 'App\Http\Controllers\WebsiteController@PolicesPage');
                    Route::post('UpdatePolices/{id}', 'App\Http\Controllers\WebsiteController@UpdatePolices');


               //Terms
                    Route::get('Terms', 'App\Http\Controllers\WebsiteController@TermsPage');
                    Route::post('UpdateTerms/{id}', 'App\Http\Controllers\WebsiteController@UpdateTerms');

                   // CouponCode
    Route::get('CouponCode', 'App\Http\Controllers\WebsiteController@CouponCodePage');
    Route::post('AddCouponCode', 'App\Http\Controllers\WebsiteController@AddCouponCode');
    Route::post('EditCouponCode/{id}', 'App\Http\Controllers\WebsiteController@EditCouponCode');
    Route::get('DeleteCouponCode/{id}', 'App\Http\Controllers\WebsiteController@DeleteCouponCode');

            //FAQ
                    Route::get('FAQ', 'App\Http\Controllers\WebsiteController@FAQPage');
                    Route::post('AddFAQ', 'App\Http\Controllers\WebsiteController@AddFAQ');
                    Route::post('EditFAQ/{id}', 'App\Http\Controllers\WebsiteController@EditFAQ');
                    Route::get('DeleteFAQ/{id}', 'App\Http\Controllers\WebsiteController@DeleteFAQ');

            //Features
                    Route::get('Features', 'App\Http\Controllers\AdminController@Features');
                    Route::post('EditFeatures/{id}', 'App\Http\Controllers\AdminController@EditFeatures');

            //HowWeWork (Why Choose Us)
                    Route::get('HowWeWork', 'App\Http\Controllers\AdminController@HowWeWork');
                    Route::post('AddHowWeWork', 'App\Http\Controllers\AdminController@AddHowWeWork');
                    Route::post('EditHowWorkIcons/{id}', 'App\Http\Controllers\AdminController@EditHowWorkIcons');

            //Countris
                    Route::get('Countris', 'App\Http\Controllers\WebsiteController@CountrisPage');
                    Route::post('AddCountris', 'App\Http\Controllers\WebsiteController@AddCountris');
                    Route::post('EditCountris/{id}', 'App\Http\Controllers\WebsiteController@EditCountris');
                    Route::get('DeleteCountris/{id}', 'App\Http\Controllers\WebsiteController@DeleteCountris');


            //ProDetailsImg
                    Route::get('ProDetailsImg', 'App\Http\Controllers\WebsiteController@ProDetailsImg');
                    Route::post('EditProDetailsImg/{id}', 'App\Http\Controllers\WebsiteController@EditProDetailsImg');


            //BefroeFooter
                    Route::get('BefroeFooter', 'App\Http\Controllers\WebsiteController@BefroeFooter');
                    Route::post('EditBefroeFooter/{id}', 'App\Http\Controllers\WebsiteController@EditBefroeFooter');

            //Gallery
                    Route::get('Gallery', 'App\Http\Controllers\WebsiteController@GalleryPage');
                    Route::post('AddGallery', 'App\Http\Controllers\WebsiteController@AddGallery');
                    Route::post('UpdateGallery/{id}', 'App\Http\Controllers\WebsiteController@UpdateGallery');
                    Route::get('DeleteGallery/{id}', 'App\Http\Controllers\WebsiteController@DeleteGallery');

            //Services
                    Route::get('Services', 'App\Http\Controllers\WebsiteController@ServicesPage');
                    Route::post('AddService', 'App\Http\Controllers\WebsiteController@AddService');
                    Route::post('UpdateService/{id}', 'App\Http\Controllers\WebsiteController@UpdateService');
                    Route::get('DeleteService/{id}', 'App\Http\Controllers\WebsiteController@DeleteService');

            //Blog
                    Route::get('Blog', 'App\Http\Controllers\WebsiteController@BlogPage');
                    Route::post('AddBlog', 'App\Http\Controllers\WebsiteController@AddBlog');
                    Route::post('UpdateBlog/{id}', 'App\Http\Controllers\WebsiteController@UpdateBlog');
                    Route::get('DeleteBlog/{id}', 'App\Http\Controllers\WebsiteController@DeleteBlog');

            //Team
                    Route::get('Team', 'App\Http\Controllers\WebsiteController@TeamPage');
                    Route::post('AddTeam', 'App\Http\Controllers\WebsiteController@AddTeam');
                    Route::post('UpdateTeam/{id}', 'App\Http\Controllers\WebsiteController@UpdateTeam');
                    Route::get('DeleteTeam/{id}', 'App\Http\Controllers\WebsiteController@DeleteTeam');

            //Testimonials
                    Route::get('Testimonials', 'App\Http\Controllers\WebsiteController@TestimonialsPage');
                    Route::post('AddTestimonial', 'App\Http\Controllers\WebsiteController@AddTestimonial');
                    Route::post('UpdateTestimonial/{id}', 'App\Http\Controllers\WebsiteController@UpdateTestimonial');
                    Route::get('DeleteTestimonial/{id}', 'App\Http\Controllers\WebsiteController@DeleteTestimonial');

            //Website Features
                    Route::get('WebsiteFeatures', 'App\Http\Controllers\WebsiteController@WebsiteFeaturesPage');
                    Route::post('AddFeature', 'App\Http\Controllers\WebsiteController@AddFeature');
                    Route::post('UpdateFeature/{id}', 'App\Http\Controllers\WebsiteController@UpdateFeature');
                    Route::get('DeleteFeature/{id}', 'App\Http\Controllers\WebsiteController@DeleteFeature');

//ShopOrders
    Route::get('ShopOrders', 'App\Http\Controllers\WebsiteController@ShopOrders');
    Route::post('ChangeStatusShop', 'App\Http\Controllers\WebsiteController@ChangeStatusShop');



//Contact Update
    Route::post('ContactUSUpdate/{id}', 'App\Http\Controllers\WebsiteController@ContactUSUpdate');

//Articles
Route::post('AddArticles', 'App\Http\Controllers\WebsiteController@AddArticles');
Route::post('EditArticles/{id}', 'App\Http\Controllers\WebsiteController@EditArticles');


//About Update
Route::post('UpdateAbout/{id}', 'App\Http\Controllers\WebsiteController@UpdateAbout');

//EComDesign
          Route::get('EComDesign', 'App\Http\Controllers\WebsiteController@EComDesign');
          Route::post('AddMainEComDesign', 'App\Http\Controllers\WebsiteController@AddMainEComDesign');
          Route::get('AddMainEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddMainEComDesignFirst');
           Route::post('AddHomeEComDesign', 'App\Http\Controllers\WebsiteController@AddHomeEComDesign');
          Route::get('AddHomeEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddHomeEComDesignFirst');
           Route::post('AddHomeProductEComDesign', 'App\Http\Controllers\WebsiteController@AddHomeProductEComDesign');
          Route::get('AddHomeProductEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddHomeProductEComDesignFirst');
           Route::post('AddSupPagesEComDesign', 'App\Http\Controllers\WebsiteController@AddSupPagesEComDesign');
          Route::get('AddSupPagesEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddSupPagesEComDesignFirst');
           Route::post('AddSupPagesPartTwoEComDesign', 'App\Http\Controllers\WebsiteController@AddSupPagesPartTwoEComDesign');
          Route::get('AddSupPagesPartTwoEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddSupPagesPartTwoEComDesignFirst');
           Route::post('AddProductDetailsEComDesign', 'App\Http\Controllers\WebsiteController@AddProductDetailsEComDesign');
          Route::get('AddProductDetailsEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddProductDetailsEComDesignFirst');
           Route::post('AddSupPagesWishCompEComDesign', 'App\Http\Controllers\WebsiteController@AddSupPagesWishCompEComDesign');
          Route::get('AddPagesWishCompEComDesignFirst', 'App\Http\Controllers\WebsiteController@AddPagesWishCompEComDesignFirst');


 // === End Website =========================================================================================================


 // === Shipping =========================================================================================================

          //Shipping Type
    Route::get('ShippingType', 'App\Http\Controllers\ShippingController@ShippingTypePage');
    Route::post('AddShippingType', 'App\Http\Controllers\ShippingController@AddShippingType');
    Route::post('EditShippingType/{id}', 'App\Http\Controllers\ShippingController@EditShippingType');
    Route::get('DeleteShippingType/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingType');

            //Shipping Status
    Route::get('ShippingStatus', 'App\Http\Controllers\ShippingController@ShippingStatusPage');
    Route::post('AddShippingStatus', 'App\Http\Controllers\ShippingController@AddShippingStatus');
    Route::post('EditShippingStatus/{id}', 'App\Http\Controllers\ShippingController@EditShippingStatus');
    Route::get('DeleteShippingStatus/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingStatus');

    //Shipping Order
    Route::get('ShippingOrder', 'App\Http\Controllers\ShippingController@ShippingOrderPage');
    Route::get('ClientPlaceFilter/{client}/{weight}/{address}', 'App\Http\Controllers\ShippingController@ClientPlaceFilter');
    Route::post('AddShippingOrder', 'App\Http\Controllers\ShippingController@AddShippingOrder');
    Route::post('EditShippingOrder/{id}', 'App\Http\Controllers\ShippingController@EditShippingOrder');
    Route::get('DeleteShippingOrder/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingOrder');
     Route::get('AddressFilter/{id}', 'App\Http\Controllers\ShippingController@AddressFilter');
     Route::get('SureCustomerCollection/{id}', 'App\Http\Controllers\ShippingController@SureCustomerCollection');
     Route::post('VendorCollectShipping', 'App\Http\Controllers\ShippingController@VendorCollectShipping');
     Route::post('CustomerCollectionRequests', 'App\Http\Controllers\ShippingController@CustomerCollectionRequests');


        //My Orders
    Route::get('MyOrdersEmp', 'App\Http\Controllers\ShippingController@MyOrdersEmpPage');
    Route::post('ChangeStatusOrder', 'App\Http\Controllers\ShippingController@ChangeStatusOrder');
    Route::post('CustomerCollection', 'App\Http\Controllers\ShippingController@CustomerCollection');
    Route::get('PrintShip/{id}', 'App\Http\Controllers\ShippingController@PrintShip');
    Route::post('CancelOrder', 'App\Http\Controllers\ShippingController@CancelOrder');
    Route::post('RequestDone', 'App\Http\Controllers\ShippingController@RequestDone');



    //Report My Orders
     Route::get('ReportMyOrdersEmp', 'App\Http\Controllers\ShippingController@ReportMyOrdersEmpPage');
     Route::get('FilterReportMyOrders', 'App\Http\Controllers\ShippingController@FilterReportMyOrders');



    //Vendor Collections
    Route::get('VendorCoolections', 'App\Http\Controllers\ShippingController@VendorCoolectionsPage');
    Route::post('PostVendorCollection', 'App\Http\Controllers\ShippingController@PostVendorCollection');

    //ReportOrders
      Route::get('ReportOrders', 'App\Http\Controllers\ShippingController@ReportOrdersPage');
      Route::get('FilterOrders', 'App\Http\Controllers\ShippingController@FilterOrders');


        //Tickets
            Route::get('TicketsSechdule', 'App\Http\Controllers\ShippingController@TicketsSechdule');
            Route::get('FilterTicketsSechdule', 'App\Http\Controllers\ShippingController@FilterTicketsSechdule');
            Route::get('Tickets', 'App\Http\Controllers\ShippingController@TicketsPage');
            Route::get('ShippingTicketProductsFilter', 'App\Http\Controllers\ShippingController@ShippingTicketProductsFilter');
            Route::get('UnitShippingProFilter/{countryId}/{Pro}', 'App\Http\Controllers\ShippingController@UnitShippingProFilter');
            Route::get('AddressPhoneClient/{countryId}', 'App\Http\Controllers\ShippingController@AddressPhoneClient');
            Route::post('AddTicket', 'App\Http\Controllers\ShippingController@AddTicket');
            Route::post('PostEditTicket', 'App\Http\Controllers\ShippingController@PostEditTicket');
            Route::get('TicketPrint/{id}', 'App\Http\Controllers\ShippingController@TicketPrint');
            Route::get('TicketPrint5/{id}', 'App\Http\Controllers\ShippingController@TicketPrint5');
            Route::get('TicketPrint8/{id}', 'App\Http\Controllers\ShippingController@TicketPrint8');
            Route::get('TicketEdit/{id}', 'App\Http\Controllers\ShippingController@TicketEdit');
            Route::get('DeleteTicket/{id}', 'App\Http\Controllers\ShippingController@DeleteTicket');

            Route::get('TicketEdit/1/ShippingTicketProductsFilter', 'App\Http\Controllers\ShippingController@ShippingTicketProductsFilter');
            Route::get('TicketEdit/UnitShippingProFilter/{countryId}/{Pro}', 'App\Http\Controllers\ShippingController@UnitShippingProFilter');
            Route::get('TicketEdit/AddressPhoneClient/{countryId}', 'App\Http\Controllers\ShippingController@AddressPhoneClient');


        //ShippingList
            Route::get('ShippingListSechdule', 'App\Http\Controllers\ShippingController@ShippingListSechdule');
            Route::get('ShippingList', 'App\Http\Controllers\ShippingController@ShippingList');
            Route::get('ShippingListFilter', 'App\Http\Controllers\ShippingController@ShippingListFilter');
            Route::get('ShippingListEdit/ShippingListFilter', 'App\Http\Controllers\ShippingController@ShippingListFilter');
            Route::post('AddShippingList', 'App\Http\Controllers\ShippingController@AddShippingList');
            Route::post('PostEditShippingList', 'App\Http\Controllers\ShippingController@PostEditShippingList');
            Route::get('ShippingListPrint/{id}', 'App\Http\Controllers\ShippingController@ShippingListPrint');
            Route::get('ShippingListEdit/{id}', 'App\Http\Controllers\ShippingController@ShippingListEdit');
            Route::get('DeleteShippingList/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingList');
            Route::get('ShippingListStatus/{id}', 'App\Http\Controllers\ShippingController@ShippingListStatus');

        //ShipmentReceipts
             Route::get('ShipmentReceiptsSechdule', 'App\Http\Controllers\ShippingController@ShipmentReceiptsSechdule');
             Route::get('ShipmentReceipts', 'App\Http\Controllers\ShippingController@ShipmentReceipts');
             Route::get('ShippingIDFilter', 'App\Http\Controllers\ShippingController@ShippingIDFilter');
             Route::get('ShipmentReceiptsEdit/ShippingIDFilter', 'App\Http\Controllers\ShippingController@ShippingIDFilter');
             Route::post('AddShipmentReceipts', 'App\Http\Controllers\ShippingController@AddShipmentReceipts');
             Route::post('PostEditShipmentReceipts', 'App\Http\Controllers\ShippingController@PostEditShipmentReceipts');
             Route::get('ShipmentReceiptsPrint/{id}', 'App\Http\Controllers\ShippingController@ShipmentReceiptsPrint');
             Route::get('ShipmentReceiptsEdit/{id}', 'App\Http\Controllers\ShippingController@ShipmentReceiptsEdit');
             Route::get('DeleteShipmentReceipts/{id}', 'App\Http\Controllers\ShippingController@DeleteShipmentReceipts');


        //ShipmentReceiptsClients
           Route::get('ShipmentReceiptsClientsSechdule', 'App\Http\Controllers\ShippingController@ShipmentReceiptsClientsSechdule');
           Route::get('ShipmentReceiptsClients', 'App\Http\Controllers\ShippingController@ShipmentReceiptsClients');
           Route::get('ShippingReciptFilter', 'App\Http\Controllers\ShippingController@ShippingReciptFilter');
           Route::get('ShippingReciptTicketFilter', 'App\Http\Controllers\ShippingController@ShippingReciptTicketFilter');
           Route::post('AddShipmentReceiptsClients', 'App\Http\Controllers\ShippingController@AddShipmentReceiptsClients');
           Route::get('ShipmentReceiptsClientsPrint/{id}', 'App\Http\Controllers\ShippingController@ShipmentReceiptsClientsPrint');


 // === End Shipping =========================================================================================================




 // === Electonic Bill =========================================================================================================


//Send_Bill_Sales
        Route::get('Send_Bill_Sales', 'App\Http\Controllers\ElectronicBillController@Send_Bill_Sales');
        Route::get('Send_Bill_ReturnSales', 'App\Http\Controllers\ElectronicBillController@Send_Bill_ReturnSales');
        Route::get('Send_Bill_SalesFilter', 'App\Http\Controllers\ElectronicBillController@Send_Bill_SalesFilter');
        Route::get('ReturnSend_Bill_SalesFilter', 'App\Http\Controllers\ElectronicBillController@ReturnSend_Bill_SalesFilter');

//Bill_Purchases_Sent
          Route::get('Bill_Purchases_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_Purchases_Sent');
//Bill_Sales_Sent
          Route::get('Bill_Sales_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_Sent');
          Route::get('Bill_ReturnSales_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_ReturnSales_Sent');
          Route::get('Bill_Sales_SentFilter', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_SentFilter');
          Route::get('Bill_ReturnSales_SentFilter', 'App\Http\Controllers\ElectronicBillController@Bill_ReturnSales_SentFilter');
          Route::get('Bill_Sales_Sent_Web', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_Sent_Web');
          Route::get('FilterBill_Sales_Sent_Web', 'App\Http\Controllers\ElectronicBillController@FilterBill_Sales_Sent_Web');
          Route::get('printPDFSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@printPDFSalesElectronic');
          Route::get('FilterprintPDFSalesElectronic/{uuid}/{longid}', 'App\Http\Controllers\ElectronicBillController@FilterprintPDFSalesElectronic');
          Route::get('printPDFSalesElectronic/FilterprintPDFSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@FilterprintPDFSalesElectronic');
          Route::get('CancelSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@CancelSalesElectronic');
          Route::get('RejectSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@RejectSalesElectronic');

                //SendElectronicBill
         Route::get('postSendSales', 'App\Http\Controllers\ElectronicBillController@postSendSales');
         Route::get('postSendSalesEXP', 'App\Http\Controllers\ElectronicBillController@postSendSalesEXP');
         Route::get('SendSalesBill/{id}', 'App\Http\Controllers\ElectronicBillController@SendSalesBill');
         Route::get('ReturnSendSalesBill/{id}', 'App\Http\Controllers\ElectronicBillController@ReturnSendSalesBill');



        //Recipt
        Route::get('Send_Recipt_Sales', 'App\Http\Controllers\ElectronicBillController@Send_Recipt_Sales');
         Route::get('SendSalesRecipt/{id}', 'App\Http\Controllers\ElectronicBillController@SendSalesRecipt');
         Route::get('Recipt_Sales_Sent', 'App\Http\Controllers\ElectronicBillController@Recipt_Sales_Sent');
         Route::get('FilterRecipt_Sales_Sent_Web', 'App\Http\Controllers\ElectronicBillController@FilterRecipt_Sales_Sent_Web');

        //ReturnSend_Recipt_Sales
           Route::get('ReturnSend_Recipt_Sales', 'App\Http\Controllers\ElectronicBillController@ReturnSend_Recipt_Sales');
           Route::get('ReturnSend_Recipt_SalesFilter', 'App\Http\Controllers\ElectronicBillController@ReturnSend_Recipt_SalesFilter');
           Route::get('ReturnSendSalesRecipt/{id}', 'App\Http\Controllers\ElectronicBillController@ReturnSendSalesRecipt');

 // === End Electronic Bill  =========================================================================================================



 // === Traning Center  =========================================================================================================

//    CoursesCategory
            Route::get('CoursesCategory', 'App\Http\Controllers\CenterController@CoursesCategory');
          Route::post('AddCoursesCategory', 'App\Http\Controllers\CenterController@AddCoursesCategory');
          Route::post('EditCoursesCategory/{id}', 'App\Http\Controllers\CenterController@EditCoursesCategory');
          Route::get('DeleteCoursesCategory/{id}', 'App\Http\Controllers\CenterController@DeleteCoursesCategory');

//ScientificMaterial
          Route::get('ScientificMaterial', 'App\Http\Controllers\CenterController@ScientificMaterial');
          Route::post('AddScientificMaterial', 'App\Http\Controllers\CenterController@AddScientificMaterial');
          Route::post('EditScientificMaterial/{id}', 'App\Http\Controllers\CenterController@EditScientificMaterial');
          Route::get('DeleteScientificMaterial/{id}', 'App\Http\Controllers\CenterController@DeleteScientificMaterial');

//StudentGroup
            Route::get('StudentGroup', 'App\Http\Controllers\CenterController@StudentGroup');
          Route::post('AddStudentGroup', 'App\Http\Controllers\CenterController@AddStudentGroup');
          Route::post('EditStudentGroup/{id}', 'App\Http\Controllers\CenterController@EditStudentGroup');
          Route::get('DeleteStudentGroup/{id}', 'App\Http\Controllers\CenterController@DeleteStudentGroup');

//CoursesType
           Route::get('CoursesType', 'App\Http\Controllers\CenterController@CoursesType');
          Route::post('AddCoursesType', 'App\Http\Controllers\CenterController@AddCoursesType');
          Route::post('EditCoursesType/{id}', 'App\Http\Controllers\CenterController@EditCoursesType');
          Route::get('DeleteCoursesType/{id}', 'App\Http\Controllers\CenterController@DeleteCoursesType');

//SpecialCases
            Route::get('SpecialCases', 'App\Http\Controllers\CenterController@SpecialCases');
          Route::post('AddSpecialCases', 'App\Http\Controllers\CenterController@AddSpecialCases');
          Route::post('EditSpecialCases/{id}', 'App\Http\Controllers\CenterController@EditSpecialCases');
          Route::get('DeleteSpecialCases/{id}', 'App\Http\Controllers\CenterController@DeleteSpecialCases');

//CoursesHalls
          Route::get('CoursesHalls', 'App\Http\Controllers\CenterController@CoursesHalls');
          Route::post('AddCoursesHalls', 'App\Http\Controllers\CenterController@AddCoursesHalls');
          Route::post('EditCoursesHalls/{id}', 'App\Http\Controllers\CenterController@EditCoursesHalls');
          Route::get('DeleteCoursesHalls/{id}', 'App\Http\Controllers\CenterController@DeleteCoursesHalls');


    //Teachers
            Route::get('TeachersSechdule', 'App\Http\Controllers\CenterController@TeachersSechdule');
            Route::get('Teachers', 'App\Http\Controllers\CenterController@Teachers');
            Route::post('AddTeachers', 'App\Http\Controllers\CenterController@AddTeachers');
            Route::get('DeleteTeachers/{id}', 'App\Http\Controllers\CenterController@DeleteTeachers');
            Route::get('EditTeacher/{id}', 'App\Http\Controllers\CenterController@EditTeacher');
            Route::post('PostEditTeachers', 'App\Http\Controllers\CenterController@PostEditTeachers');

        //Students
          Route::get('StudentsSechdule', 'App\Http\Controllers\CenterController@StudentsSechdule');
          Route::get('StudentReserveCourse/AddNewStudent', 'App\Http\Controllers\CenterController@AddNewStudent');

          Route::get('StudentReserveCourse/AllStudent', 'App\Http\Controllers\CenterController@AllStudent');
          Route::get('StudentReserveCourse/AllStudentJ/{id}', 'App\Http\Controllers\CenterController@AllStudentJ');



          Route::get('Students', 'App\Http\Controllers\CenterController@Students');
            Route::post('AddStudents', 'App\Http\Controllers\CenterController@AddStudents');
            Route::get('DeleteStudents/{id}', 'App\Http\Controllers\CenterController@DeleteStudents');
              Route::get('EditStudent/{id}', 'App\Http\Controllers\CenterController@EditStudent');
            Route::post('PostEditStudent', 'App\Http\Controllers\CenterController@PostEditStudent');

        //Courses
          Route::get('Courses', 'App\Http\Controllers\CenterController@Courses');
          Route::get('CoursesDetails', 'App\Http\Controllers\CenterController@CoursesDetails');
          Route::post('AddCourses', 'App\Http\Controllers\CenterController@AddCourses');
          Route::post('EditCourses/{id}', 'App\Http\Controllers\CenterController@EditCourses');
          Route::get('DeleteCourses/{id}', 'App\Http\Controllers\CenterController@DeleteCourses');


        //ReserveCourse
             Route::get('ReserveCourseSechdule', 'App\Http\Controllers\CenterController@ReserveCourseSechdule');
             Route::get('ReserveCourse', 'App\Http\Controllers\CenterController@ReserveCourse');
             Route::post('AddReserveCourse', 'App\Http\Controllers\CenterController@AddReserveCourse');
             Route::post('PostEditReserveCourse', 'App\Http\Controllers\CenterController@PostEditReserveCourse');
            Route::get('DeleteReserveCourse/{id}', 'App\Http\Controllers\CenterController@DeleteReserveCourse');
            Route::get('ComplitedReserveCourse/{id}', 'App\Http\Controllers\CenterController@ComplitedReserveCourse');
            Route::get('UnComplitedReserveCourse/{id}', 'App\Http\Controllers\CenterController@UnComplitedReserveCourse');
            Route::get('EditResreveCourse/{id}', 'App\Http\Controllers\CenterController@EditResreveCourse');
            Route::get('StudentReserveCourse/{id}', 'App\Http\Controllers\CenterController@StudentReserveCourse');
           Route::post('PostStudentReserveCourse', 'App\Http\Controllers\CenterController@PostStudentReserveCourse');
           Route::get('StudentPrintBill/{id}', 'App\Http\Controllers\CenterController@StudentPrintBill');
           Route::get('OtherReserv', 'App\Http\Controllers\CenterController@OtherReserv');

        //RegCourses
            Route::get('RegCoursesSechdule', 'App\Http\Controllers\CenterController@RegCoursesSechdule');
            Route::get('RegCourses', 'App\Http\Controllers\CenterController@RegCourses');
           Route::post('AddRegCourses', 'App\Http\Controllers\CenterController@AddRegCourses');
           Route::post('PostEditRegCourses', 'App\Http\Controllers\CenterController@PostEditRegCourses');
          Route::get('DeleteRegCourses/{id}', 'App\Http\Controllers\CenterController@DeleteRegCourses');
          Route::get('EditRegCourses/{id}', 'App\Http\Controllers\CenterController@EditRegCourses');
          Route::get('Reserve_CourseFilter', 'App\Http\Controllers\CenterController@Reserve_CourseFilter');
          Route::get('EditRegCourses/{id}/Reserve_CourseFilter', 'App\Http\Controllers\CenterController@Reserve_CourseFilter');
 // === End Traning Center  =========================================================================================================



 // === Translate  =========================================================================================================



//PurposeTravel
          Route::get('PurposeTravel', 'App\Http\Controllers\TranslateController@PurposeTravel');
          Route::post('AddPurposeTravel', 'App\Http\Controllers\TranslateController@AddPurposeTravel');
          Route::post('EditPurposeTravel/{id}', 'App\Http\Controllers\TranslateController@EditPurposeTravel');
          Route::get('DeletePurposeTravel/{id}', 'App\Http\Controllers\TranslateController@DeletePurposeTravel');


//Languages
          Route::get('Languages', 'App\Http\Controllers\TranslateController@Languages');
          Route::post('AddLanguages', 'App\Http\Controllers\TranslateController@AddLanguages');
          Route::post('EditLanguages/{id}', 'App\Http\Controllers\TranslateController@EditLanguages');
          Route::get('DeleteLanguages/{id}', 'App\Http\Controllers\TranslateController@DeleteLanguages');


//Empassies
          Route::get('Empassies', 'App\Http\Controllers\TranslateController@Empassies');
          Route::post('AddEmpassies', 'App\Http\Controllers\TranslateController@AddEmpassies');
          Route::post('EditEmpassies/{id}', 'App\Http\Controllers\TranslateController@EditEmpassies');
          Route::get('DeleteEmpassies/{id}', 'App\Http\Controllers\TranslateController@DeleteEmpassies');


//TranslationTourismCompanies
          Route::get('TranslationTourismCompanies', 'App\Http\Controllers\TranslateController@TranslationTourismCompanies');
          Route::post('AddTranslationTourismCompanies', 'App\Http\Controllers\TranslateController@AddTranslationTourismCompanies');
          Route::post('EditTranslationTourismCompanies/{id}', 'App\Http\Controllers\TranslateController@EditTranslationTourismCompanies');
          Route::get('DeleteTranslationTourismCompanies/{id}', 'App\Http\Controllers\TranslateController@DeleteTranslationTourismCompanies');


        //TranslteModules
          Route::get('TranslteModules', 'App\Http\Controllers\TranslateController@TranslteModules');
          Route::post('AddTranslteModules', 'App\Http\Controllers\TranslateController@AddTranslteModules');
          Route::post('EditTranslteModules/{id}', 'App\Http\Controllers\TranslateController@EditTranslteModules');
          Route::get('DeleteTranslteModules/{id}', 'App\Http\Controllers\TranslateController@DeleteTranslteModules');


//EmpassyReserveDate
              Route::get('EmpassyReserveDate', 'App\Http\Controllers\TranslateController@EmpassyReserveDate');
          Route::post('AddEmpassyReserveDate', 'App\Http\Controllers\TranslateController@AddEmpassyReserveDate');
          Route::post('EditEmpassyReserveDate/{id}', 'App\Http\Controllers\TranslateController@EditEmpassyReserveDate');
          Route::get('DeleteEmpassyReserveDate/{id}', 'App\Http\Controllers\TranslateController@DeleteEmpassyReserveDate');


        //AddTranslate
                 Route::get('AddTranslate', 'App\Http\Controllers\TranslateController@AddTranslate');
          Route::post('AddAddTranslate', 'App\Http\Controllers\TranslateController@AddAddTranslate');
          Route::post('EditAddTranslate/{id}', 'App\Http\Controllers\TranslateController@EditAddTranslate');
          Route::get('DeleteAddTranslate/{id}', 'App\Http\Controllers\TranslateController@DeleteAddTranslate');



 // === End Translate  =========================================================================================================



 // === Hotels  =========================================================================================================



           //RoomsType
          Route::get('RoomsType', 'App\Http\Controllers\HotelController@RoomsType');
          Route::post('AddRoomsType', 'App\Http\Controllers\HotelController@AddRoomsType');
          Route::post('EditRoomsType/{id}', 'App\Http\Controllers\HotelController@EditRoomsType');
          Route::get('DeleteRoomsType/{id}', 'App\Http\Controllers\HotelController@DeleteRoomsType');

        //Rooms
          Route::get('Rooms', 'App\Http\Controllers\HotelController@Rooms');
          Route::post('AddRooms', 'App\Http\Controllers\HotelController@AddRooms');
          Route::post('EditRooms/{id}', 'App\Http\Controllers\HotelController@EditRooms');
          Route::get('DeleteRooms/{id}', 'App\Http\Controllers\HotelController@DeleteRooms');


//Reservations
          Route::get('Reservations', 'App\Http\Controllers\HotelController@Reservations');
          Route::get('RoomsFilter', 'App\Http\Controllers\HotelController@RoomsFilter');
          Route::get('NewClientHotel', 'App\Http\Controllers\HotelController@NewClientHotel');
          Route::get('EditReserv/NewClientHotel', 'App\Http\Controllers\HotelController@NewClientHotel');
          Route::get('EditReserv/{id}/RoomsFilter', 'App\Http\Controllers\HotelController@RoomsFilter');
          Route::get('EditReserv/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
          Route::get('EditReserv/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
          Route::get('ClientDelivery/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
          Route::post('AddResrvation', 'App\Http\Controllers\HotelController@AddResrvation');
          Route::post('PostEditResrvation', 'App\Http\Controllers\HotelController@PostEditResrvation');
          Route::get('Reservations_Sechdule', 'App\Http\Controllers\HotelController@Reservations_Sechdule');
         Route::get('EditReserv/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
         Route::get('EditReserv/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
           Route::get('EditReserv/{id}', 'App\Http\Controllers\HotelController@EditReserv');
           Route::get('DeleteReservation/{id}', 'App\Http\Controllers\HotelController@DeleteReservation');
           Route::get('CheckoutReservation/{id}', 'App\Http\Controllers\HotelController@CheckoutReservation');

        //ReservationsReport
         Route::get('ReservationsReport', 'App\Http\Controllers\HotelController@ReservationsReport');
         Route::get('ReservReportFilterrr', 'App\Http\Controllers\HotelController@ReservReportFilterrr');
 // === End Hotels  =========================================================================================================

          //Imports
          Route::get('ExportProducts', 'App\Http\Controllers\StoresController@ExportProductsPage');
          Route::post('ImportProducts', 'App\Http\Controllers\StoresController@ImportProducts');
          Route::post('ImportProductsWithStart', 'App\Http\Controllers\StoresController@ImportProductsWithStart');
          Route::post('ImportProductsUnits', 'App\Http\Controllers\StoresController@ImportProductsUnits');
          Route::post('ImportGroups', 'App\Http\Controllers\StoresController@ImportGroups');
          Route::post('ImportMoves', 'App\Http\Controllers\StoresController@ImportMoves');
          Route::post('ImportStartPeriod', 'App\Http\Controllers\StoresController@ImportStartPeriod');
          Route::post('ImportProStartPeriod', 'App\Http\Controllers\StoresController@ImportProStartPeriod');
          Route::post('ImportClients', 'App\Http\Controllers\StoresController@ImportClients');
          Route::post('ImportVendors', 'App\Http\Controllers\StoresController@ImportVendors');
          Route::post('ImportEmployees', 'App\Http\Controllers\StoresController@ImportEmployees');
          Route::post('ImportReceipt_Voucher', 'App\Http\Controllers\StoresController@ImportReceipt_Voucher');
          Route::post('ImportPayment_Voucher', 'App\Http\Controllers\StoresController@ImportPayment_Voucher');
          Route::post('ImportProductRepeat', 'App\Http\Controllers\StoresController@ImportProductRepeat');
          Route::post('ImportCustomersGroup', 'App\Http\Controllers\StoresController@ImportCustomersGroup');
          Route::post('ImportBrands', 'App\Http\Controllers\StoresController@ImportBrands');
          Route::post('ImportAccountingManual', 'App\Http\Controllers\StoresController@ImportAccountingManual');
          Route::post('ImportAssets', 'App\Http\Controllers\StoresController@ImportAssets');
          Route::post('ImportExporting_Checks', 'App\Http\Controllers\StoresController@ImportExporting_Checks');
          Route::post('ImportIncoming_checks', 'App\Http\Controllers\StoresController@ImportIncoming_checks');
          Route::post('ImportInsurance_Paper', 'App\Http\Controllers\StoresController@ImportInsurance_Paper');
          Route::post('ImportCustomerFollowUp', 'App\Http\Controllers\StoresController@ImportCustomerFollowUp');
            Route::post('ImportAttendance', 'App\Http\Controllers\StoresController@ImportAttendance');
        //Export

          Route::get('ExportStoresQtyyy', 'App\Http\Controllers\StoresController@ExportStoresQtyyy');
          Route::get('ExportStoresQtyyyRased', 'App\Http\Controllers\StoresController@ExportStoresQtyyyRased');
          Route::get('ExportStoresQtyyyRasedSerial', 'App\Http\Controllers\StoresController@ExportStoresQtyyyRasedSerial');
          Route::get('ExportStoresBalancesNew', 'App\Http\Controllers\StoresController@ExportStoresBalancesNew');
          Route::get('ExportStoresBalancesCat', 'App\Http\Controllers\StoresController@ExportStoresBalancesCat');
          Route::get('ExportDelegateSalesDetails', 'App\Http\Controllers\StoresController@ExportDelegateSalesDetails');
          Route::get('ExportProfitDelegateSalesDetails', 'App\Http\Controllers\StoresController@ExportProfitDelegateSalesDetails');
          Route::get('ExportStoresCosts', 'App\Http\Controllers\StoresController@ExportStoresCosts');
          Route::get('ExportInstallmentCompaniesSales', 'App\Http\Controllers\StoresController@ExportInstallmentCompaniesSales');
          Route::get('ExportPurchasesBillsReport', 'App\Http\Controllers\StoresController@ExportPurchasesBillsReport');
          Route::get('ExportSalesBillsReport', 'App\Http\Controllers\StoresController@ExportSalesBillsReport');
          Route::get('ExportSalesDetailsBillsReport', 'App\Http\Controllers\StoresController@ExportSalesDetailsBillsReport');
          Route::get('ExportPurchasesDetailsBillsReport', 'App\Http\Controllers\StoresController@ExportPurchasesDetailsBillsReport');
          Route::get('ExportStoresMovesReport', 'App\Http\Controllers\StoresController@ExportStoresMovesReport');
          Route::get('ExportStoresTransferReport', 'App\Http\Controllers\StoresController@ExportStoresTransferReport');
          Route::get('ExportSafesTransferReport', 'App\Http\Controllers\StoresController@ExportSafesTransferReport');
          Route::get('ExportProductMoveDetails', 'App\Http\Controllers\StoresController@ExportProductMoveDetails');
          Route::get('ExportCompareSalesPrice', 'App\Http\Controllers\StoresController@ExportCompareSalesPrice');
          Route::get('ExportMostSalesProducts', 'App\Http\Controllers\StoresController@ExportMostSalesProducts');
          Route::get('ExportProfitSalesProduct', 'App\Http\Controllers\StoresController@ExportProfitSalesProduct');
          Route::get('ExportClientsStatement', 'App\Http\Controllers\StoresController@ExportClientsStatement');
          Route::get('ExportClientAccountStatement', 'App\Http\Controllers\StoresController@ExportClientAccountStatement');
          Route::get('ExportVendorAccountStatement', 'App\Http\Controllers\StoresController@ExportVendorAccountStatement');
          Route::get('ExportVendorsStatement', 'App\Http\Controllers\StoresController@ExportVendorsStatement');
          Route::get('ExportInventorySerial', 'App\Http\Controllers\StoresController@ExportInventorySerial');
          Route::post('ExportSalesCustomersGroupsFilter', 'App\Http\Controllers\StoresController@ExportSalesCustomersGroupsFilter');
          Route::post('ExportMaintenance_Tune', 'App\Http\Controllers\StoresController@ExportMaintenance_Tune');
          Route::post('ExportMaintanceSalesReport', 'App\Http\Controllers\StoresController@ExportMaintanceSalesReport');
          Route::get('ExportReturnPurchasesBillsReport', 'App\Http\Controllers\StoresController@ExportReturnPurchasesBillsReport');
          Route::get('ExportReturnSalesBillsReport', 'App\Http\Controllers\StoresController@ExportReturnSalesBillsReport');
          Route::get('ExportGeneralDailyFilter', 'App\Http\Controllers\StoresController@ExportGeneralDailyFilter');
          Route::get('ExpensesListExport', 'App\Http\Controllers\StoresController@ExpensesListExport');
          Route::get('ExpensesListExportDetails', 'App\Http\Controllers\StoresController@ExpensesListExportDetails');
          Route::get('IncomListExport', 'App\Http\Controllers\StoresController@IncomListExport');
          Route::get('IncomListExportDetails', 'App\Http\Controllers\StoresController@IncomListExportDetails');



        //Export Sechdule
          Route::get('ExportAllProducts', 'App\Http\Controllers\StoresController@ExportAllProducts');
          Route::get('ExportAllVendors', 'App\Http\Controllers\StoresController@ExportAllVendors');
          Route::get('ExportAllPaymentVoucher', 'App\Http\Controllers\StoresController@ExportAllPaymentVoucher');
          Route::get('ExportAllJournalizing', 'App\Http\Controllers\StoresController@ExportAllJournalizing');
          Route::get('ExportAllOpeningEntries', 'App\Http\Controllers\StoresController@ExportAllOpeningEntries');
          Route::get('ExportAllReceiptVoucher', 'App\Http\Controllers\StoresController@ExportAllReceiptVoucher');
          Route::get('ExportAllSettlement', 'App\Http\Controllers\StoresController@ExportAllSettlement');
          Route::get('ExportAllClients', 'App\Http\Controllers\StoresController@ExportAllClients');
          Route::get('ExportAllEmployees', 'App\Http\Controllers\StoresController@ExportAllEmployees');



           //LoginSlider
                    Route::get('LoginSlider', 'App\Http\Controllers\AdminController@LoginSlider');
                    Route::post('AddLoginSlider', 'App\Http\Controllers\AdminController@AddLoginSlider');
                    Route::post('EditLoginSlider/{id}', 'App\Http\Controllers\AdminController@EditLoginSlider');
                    Route::get('DeleteLoginSlider/{id}', 'App\Http\Controllers\AdminController@DeleteLoginSlider');

        //Domain_Regstration
                    Route::get('Domain_Regstration', 'App\Http\Controllers\AdminController@Domain_Regstration');
                    Route::get('Updates', 'App\Http\Controllers\AdminController@Updates');
                    Route::post('NewUpdateMigration', 'App\Http\Controllers\AdminController@NewUpdateMigration');
                    Route::post('NewUpdateSeeders', 'App\Http\Controllers\AdminController@NewUpdateSeeders');
                    Route::post('AddDomain_Regstration', 'App\Http\Controllers\AdminController@AddDomain_Regstration');
                    Route::get('DeleteDomain_Regstration/{id}', 'App\Http\Controllers\AdminController@DeleteDomain_Regstration');



    });



});

    });

     });

