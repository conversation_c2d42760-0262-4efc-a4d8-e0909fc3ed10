<?php $__env->startSection('content'); ?>

<title><?php echo e(trans('admin.Shop')); ?></title>



    <!--==============================
    Breadcumb
    ============================== -->
    <div class="breadcumb-wrapper" data-bg-src="<?php echo e(asset('Front/assets/img/bg/breadcrumb-bg.png')); ?>">
        <!-- bg animated image/ -->
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcumb-content">
                        <h1 class="breadcumb-title"><?php echo e(trans('admin.Shop')); ?></h1>
                        <ul class="breadcumb-menu">
                            <li><a href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                            <li class="active"><?php echo e(trans('admin.Shop')); ?></li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--==============================
    Shop Area
    ==============================-->
    <div class="shop-area container space">
        <div class="row">
        <div class="col-lg-8">
          <div class="container">
            <div class="row gy-40">


                   <?php $__currentLoopData = $Products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="product-img">
                            <img src="<?php echo e(URL::to($pro->Image)); ?>" alt="Product Image">
                            <div class="actions">
                                <a href="<?php echo e(url('ProDetails/'.$pro->id)); ?>" class="btn style2">   <i class="fal fa-shopping-cart"></i></a>
                                <a href="<?php echo e(url('PrescriptionPro/'.$pro->id)); ?>" class="btn style2">   <i class="fas fa-file"></i></a>
                            </div>
                        </div>
                        <div class="product-content">
                            <h3 class="product-title"><a href="<?php echo e(url('ProDetails/'.$pro->id)); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?></a></h3>
                                 <?php if(!empty($pro->Offer_Price)): ?>
                            <span class="price"><del><?php echo e($pro->Price); ?></del><?php echo e($pro->Offer_Price); ?> <?php echo e($pro->Symbol); ?></span>
                            <?php else: ?>
                           <span class="price"><?php echo e($pro->Price); ?> <?php echo e($pro->Symbol); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
        </div>
           <div class="pagination justify-content-center">
                <ul>
                      <?php echo e($Products->Links()); ?>

                </ul>
            </div>

        </div>

           <div class="col-lg-4">
                <!-- Search Widget -->
                <div class="shop-sidebar-widget">
                    <h3 class="widget-title"><?php echo e(trans('admin.Search_Products')); ?></h3>
                    <div class="search-widget">
                        <form action="<?php echo e(url('ShopFilterName')); ?>" method="get" class="search-form">
                            <div class="search-input-group">
                                <input type="text"
                                       name="search"
                                       class="search-input"
                                       placeholder="<?php echo e(trans('admin.Search_for_products')); ?>"
                                       value="<?php echo e(request('search')); ?>"
                                       autocomplete="off">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <?php if(request('search')): ?>
                            <div class="search-results-info">
                                <span class="search-term"><?php echo e(trans('admin.Search_results_for')); ?>: "<?php echo e(request('search')); ?>"</span>
                                <a href="<?php echo e(url('ShopSite')); ?>" class="clear-search">
                                    <i class="fas fa-times"></i>
                                    <?php echo e(trans('admin.Clear_Search')); ?>

                                </a>
                            </div>
                            <?php endif; ?>
                        </form>

                        <!-- Quick Search Suggestions -->
                        <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                            <div class="suggestions-list"></div>
                        </div>
                    </div>
                </div>

                <!-- Categories Widget -->
                <div class="shop-sidebar-widget">
                    <h3 class="widget-title"><?php echo e(trans('admin.Categories')); ?></h3>
                    <div class="categories-widget">
                        <ul class="categories-list">
                            <li class="category-item <?php echo e(!request('category') ? 'active' : ''); ?>">
                                <a href="<?php echo e(url('ShopSite')); ?>" class="category-link">
                                    <i class="fas fa-th-large"></i>
                                    <span><?php echo e(trans('admin.All_Categories')); ?></span>
                                    <span class="product-count">(<?php echo e($Products->total()); ?>)</span>
                                </a>
                            </li>
                            <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="category-item <?php echo e(request('category') == $group->id ? 'active' : ''); ?>">
                                <a href="<?php echo e(url('FilterShopCat/'.$group->id)); ?>" class="category-link">
                                    <i class="fas fa-arrow-right"></i>
                                    <span><?php echo e(app()->getLocale() == 'ar' ? $group->Name : $group->NameEn); ?></span>
                                    <span class="product-count">(<?php echo e($group->products_count ?? 0); ?>)</span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>

                <!-- Price Filter Widget -->
                <div class="shop-sidebar-widget">
                    <h3 class="widget-title"><?php echo e(trans('admin.Price_Range')); ?></h3>
                    <div class="price-filter-widget">
                        <form action="<?php echo e(url('ShopFilterName')); ?>" method="get" class="price-filter-form">
                            <?php if(request('search')): ?>
                                <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                            <?php endif; ?>
                            <div class="price-inputs">
                                <div class="price-input-group">
                                    <label><?php echo e(trans('admin.Min_Price')); ?></label>
                                    <input type="number" name="min_price" class="price-input"
                                           placeholder="0" value="<?php echo e(request('min_price')); ?>" min="0">
                                </div>
                                <div class="price-input-group">
                                    <label><?php echo e(trans('admin.Max_Price')); ?></label>
                                    <input type="number" name="max_price" class="price-input"
                                           placeholder="1000" value="<?php echo e(request('max_price')); ?>" min="0">
                                </div>
                            </div>
                            <button type="submit" class="price-filter-btn">
                                <i class="fas fa-filter"></i>
                                <?php echo e(trans('admin.Apply_Filter')); ?>

                            </button>
                        </form>
                    </div>
                </div>

                <!-- Sort Options Widget -->
                <div class="shop-sidebar-widget">
                    <h3 class="widget-title"><?php echo e(trans('admin.Sort_By')); ?></h3>
                    <div class="sort-widget">
                        <form action="<?php echo e(url('ShopFilterName')); ?>" method="get" class="sort-form">
                            <?php if(request('search')): ?>
                                <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                            <?php endif; ?>
                            <?php if(request('min_price')): ?>
                                <input type="hidden" name="min_price" value="<?php echo e(request('min_price')); ?>">
                            <?php endif; ?>
                            <?php if(request('max_price')): ?>
                                <input type="hidden" name="max_price" value="<?php echo e(request('max_price')); ?>">
                            <?php endif; ?>

                            <div class="sort-options">
                                <div class="sort-option <?php echo e(!request('sort') || request('sort') == '' ? 'active' : ''); ?>"
                                     onclick="submitSort('')">
                                    <i class="fas fa-list"></i>
                                    <span><?php echo e(trans('admin.Default_Sorting')); ?></span>
                                </div>

                                <div class="sort-option <?php echo e(request('sort') == 'name_asc' ? 'active' : ''); ?>"
                                     onclick="submitSort('name_asc')">
                                    <i class="fas fa-sort-alpha-down"></i>
                                    <span><?php echo e(trans('admin.Name_A_to_Z')); ?></span>
                                </div>

                                <div class="sort-option <?php echo e(request('sort') == 'name_desc' ? 'active' : ''); ?>"
                                     onclick="submitSort('name_desc')">
                                    <i class="fas fa-sort-alpha-up"></i>
                                    <span><?php echo e(trans('admin.Name_Z_to_A')); ?></span>
                                </div>

                                <div class="sort-option <?php echo e(request('sort') == 'price_asc' ? 'active' : ''); ?>"
                                     onclick="submitSort('price_asc')">
                                    <i class="fas fa-sort-amount-down"></i>
                                    <span><?php echo e(trans('admin.Price_Low_to_High')); ?></span>
                                </div>

                                <div class="sort-option <?php echo e(request('sort') == 'price_desc' ? 'active' : ''); ?>"
                                     onclick="submitSort('price_desc')">
                                    <i class="fas fa-sort-amount-up"></i>
                                    <span><?php echo e(trans('admin.Price_High_to_Low')); ?></span>
                                </div>

                                <div class="sort-option <?php echo e(request('sort') == 'newest' ? 'active' : ''); ?>"
                                     onclick="submitSort('newest')">
                                    <i class="fas fa-clock"></i>
                                    <span><?php echo e(trans('admin.Newest_First')); ?></span>
                                </div>
                            </div>

                            <input type="hidden" name="sort" id="sortInput" value="<?php echo e(request('sort')); ?>">
                        </form>
                    </div>
                </div>




        </div>
       </div>
    </div>

<style>
/* Shop Sidebar Styling */
.shop-sidebar-widget {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.widget-title {
    color: #333;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #5c4b2d;
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
}

/* Search Widget Styling */
.search-input-group {
    display: flex;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #5c4b2d;
    box-shadow: 0 0 0 3px rgba(92, 75, 45, 0.1);
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    outline: none;
    font-size: 16px;
    background: #f8f9fa;
    color: #333;
    transition: all 0.3s ease;
}

.search-input:focus {
    background: white;
    color: #333;
}

.search-input::placeholder {
    color: #999;
}

.search-btn {
    background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
    border: none;
    padding: 15px 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: linear-gradient(135deg, #4a3d24 0%, #5c4b2d 100%);
    transform: scale(1.05);
}

.search-btn i {
    font-size: 16px;
}

.search-results-info {
    margin-top: 15px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.search-term {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.clear-search {
    color: #dc3545;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.clear-search:hover {
    color: #c82333;
    text-decoration: underline;
}

/* Categories Widget Styling */
.categories-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: 8px;
}

.category-link {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.category-link:hover {
    background: #f8f9fa;
    color: #5c4b2d;
    border-color: #e0e0e0;
}

.category-item.active .category-link {
    background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
    color: white;
    border-color: #5c4b2d;
}

.category-link i {
    margin-right: 10px;
    font-size: 14px;
    width: 16px;
}

.category-link span:first-of-type {
    flex: 1;
    font-weight: 500;
}

.product-count {
    font-size: 12px;
    background: rgba(92, 75, 45, 0.1);
    color: #5c4b2d;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.category-item.active .product-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Price Filter Widget Styling */
.price-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.price-input-group label {
    display: block;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.price-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #333;
}

.price-input:focus {
    border-color: #5c4b2d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(92, 75, 45, 0.1);
    background: white;
    color: #333;
}

.price-input::placeholder {
    color: #999;
}

.price-filter-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
}

.price-filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* Sort Widget Styling */
.sort-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sort-option {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 12px;
}

.sort-option:hover {
    background: #e9ecef;
    border-color: #5c4b2d;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(92, 75, 45, 0.1);
}

.sort-option.active {
    background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
    border-color: #5c4b2d;
    color: white;
    box-shadow: 0 4px 15px rgba(92, 75, 45, 0.3);
}

.sort-option i {
    font-size: 16px;
    color: #666;
    width: 20px;
    text-align: center;
}

.sort-option.active i {
    color: white;
}

.sort-option span {
    flex: 1;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.sort-option.active span {
    color: white;
    font-weight: 600;
}

/* RTL Support */
[dir="rtl"] .category-link i {
    margin-right: 0;
    margin-left: 10px;
    transform: rotate(180deg);
}

[dir="rtl"] .search-results-info {
    flex-direction: row-reverse;
}

[dir="rtl"] .clear-search {
    flex-direction: row-reverse;
}

[dir="rtl"] .widget-title::after {
    left: auto;
    right: 0;
}

[dir="rtl"] .sort-option {
    flex-direction: row-reverse;
}

[dir="rtl"] .sort-option i {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shop-sidebar-widget {
        padding: 20px;
        margin-bottom: 20px;
    }

    .search-input {
        padding: 12px 15px;
        font-size: 14px;
    }

    .search-btn {
        padding: 12px 15px;
    }

    .price-inputs {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .search-results-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .category-link {
        padding: 10px 12px;
    }

    .widget-title {
        font-size: 16px;
    }

    .sort-option {
        padding: 10px 12px;
        gap: 10px;
    }

    .sort-option i {
        font-size: 14px;
        width: 18px;
    }

    .sort-option span {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .shop-sidebar-widget {
        padding: 15px;
    }

    .search-input-group {
        flex-direction: column;
    }

    .search-btn {
        border-radius: 0 0 8px 8px;
    }

    .search-input {
        border-radius: 8px 8px 0 0;
    }
}

/* Animation for widgets */
@keyframes  fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.shop-sidebar-widget {
    animation: fadeInUp 0.5s ease forwards;
}

.shop-sidebar-widget:nth-child(1) { animation-delay: 0.1s; }
.shop-sidebar-widget:nth-child(2) { animation-delay: 0.2s; }
.shop-sidebar-widget:nth-child(3) { animation-delay: 0.3s; }
.shop-sidebar-widget:nth-child(4) { animation-delay: 0.4s; }

/* Search Suggestions Styling */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 5px;
}

.suggestions-list {
    padding: 10px 0;
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item i {
    color: #999;
    font-size: 14px;
}

.suggestion-text {
    flex: 1;
    color: #333;
}

.suggestion-category {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 10px;
}

/* Loading state */
.search-loading {
    padding: 15px;
    text-align: center;
    color: #666;
}

.search-loading i {
    animation: spin 1s linear infinite;
}

@keyframes  spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No results state */
.no-suggestions {
    padding: 15px;
    text-align: center;
    color: #999;
    font-style: italic;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.getElementById('searchSuggestions');
    const suggestionsList = document.querySelector('.suggestions-list');
    let searchTimeout;

    if (searchInput) {
        // Search suggestions functionality
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    fetchSearchSuggestions(query);
                }, 300);
            } else {
                hideSearchSuggestions();
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                hideSearchSuggestions();
            }
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            const suggestions = document.querySelectorAll('.suggestion-item');
            let currentIndex = -1;

            suggestions.forEach((item, index) => {
                if (item.classList.contains('active')) {
                    currentIndex = index;
                }
            });

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentIndex = currentIndex < suggestions.length - 1 ? currentIndex + 1 : 0;
                updateActiveSuggestion(suggestions, currentIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : suggestions.length - 1;
                updateActiveSuggestion(suggestions, currentIndex);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                const activeSuggestion = document.querySelector('.suggestion-item.active');
                if (activeSuggestion) {
                    searchInput.value = activeSuggestion.querySelector('.suggestion-text').textContent;
                    hideSearchSuggestions();
                    searchInput.closest('form').submit();
                }
            } else if (e.key === 'Escape') {
                hideSearchSuggestions();
            }
        });
    }

    function fetchSearchSuggestions(query) {
        showSearchLoading();

        // Simulate API call - replace with actual endpoint
        setTimeout(() => {
            const mockSuggestions = [
                { text: query + ' product 1', category: 'Electronics' },
                { text: query + ' product 2', category: 'Clothing' },
                { text: query + ' product 3', category: 'Books' }
            ];

            displaySearchSuggestions(mockSuggestions);
        }, 500);
    }

    function showSearchLoading() {
        suggestionsList.innerHTML = '<div class="search-loading"><i class="fas fa-spinner"></i> <?php echo e(trans("admin.Searching")); ?>...</div>';
        searchSuggestions.style.display = 'block';
    }

    function displaySearchSuggestions(suggestions) {
        if (suggestions.length === 0) {
            suggestionsList.innerHTML = '<div class="no-suggestions"><?php echo e(trans("admin.No_suggestions_found")); ?></div>';
        } else {
            suggestionsList.innerHTML = suggestions.map(suggestion => `
                <div class="suggestion-item" onclick="selectSuggestion('${suggestion.text}')">
                    <i class="fas fa-search"></i>
                    <span class="suggestion-text">${suggestion.text}</span>
                    <span class="suggestion-category">${suggestion.category}</span>
                </div>
            `).join('');
        }
        searchSuggestions.style.display = 'block';
    }

    function hideSearchSuggestions() {
        searchSuggestions.style.display = 'none';
    }

    function updateActiveSuggestion(suggestions, activeIndex) {
        suggestions.forEach((item, index) => {
            item.classList.toggle('active', index === activeIndex);
        });
    }

    window.selectSuggestion = function(text) {
        searchInput.value = text;
        hideSearchSuggestions();
        searchInput.closest('form').submit();
    };

    // Sort option functionality
    window.submitSort = function(sortValue) {
        const sortInput = document.getElementById('sortInput');
        const sortForm = document.querySelector('.sort-form');

        if (sortInput && sortForm) {
            sortInput.value = sortValue;
            sortForm.submit();
        }
    };

    // Price filter validation
    const minPriceInput = document.querySelector('input[name="min_price"]');
    const maxPriceInput = document.querySelector('input[name="max_price"]');

    if (minPriceInput && maxPriceInput) {
        minPriceInput.addEventListener('input', function() {
            const minValue = parseFloat(this.value);
            const maxValue = parseFloat(maxPriceInput.value);

            if (maxValue && minValue > maxValue) {
                maxPriceInput.value = minValue;
            }
        });

        maxPriceInput.addEventListener('input', function() {
            const maxValue = parseFloat(this.value);
            const minValue = parseFloat(minPriceInput.value);

            if (minValue && maxValue < minValue) {
                minPriceInput.value = maxValue;
            }
        });
    }

    // Smooth scroll to results after filter
    if (window.location.search.includes('search=') ||
        window.location.search.includes('min_price=') ||
        window.location.search.includes('max_price=') ||
        window.location.search.includes('sort=')) {

        setTimeout(() => {
            const resultsSection = document.querySelector('.col-lg-8');
            if (resultsSection) {
                resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }, 100);
    }
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/Shop.blade.php ENDPATH**/ ?>